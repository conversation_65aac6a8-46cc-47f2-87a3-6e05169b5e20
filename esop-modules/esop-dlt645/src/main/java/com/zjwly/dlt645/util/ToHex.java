package com.zjwly.dlt645.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;


public class ToHex {

	private static Logger log = LoggerFactory.getLogger(ToHex.class);

	static char[] BCDCode = new char[] { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F',
			'0' };

	public static String ToHex(byte[] bytes) {
		int[] read_ints = new int[bytes.length];
		for (int i = 0; i < bytes.length; i++) {
			if ((int) bytes[i] > 0) {
				read_ints[i] = (int) bytes[i];
			} else {
				read_ints[i] = (int) bytes[i] + 256;
			}
		}
		log.info(Arrays.toString(read_ints));
		String str_addr = "";
		String EachByteAddress;
		for (int addr_b : read_ints) {
			EachByteAddress = BCDCode[addr_b / 16] + "" + BCDCode[addr_b % 16] + " ";
			str_addr = str_addr + EachByteAddress;
		}
		return "[ " + str_addr + " ]";
	}
}
