package com.zjwly.dlt645.server.slave;

import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;

/**
 * DL/T 645 TCP 协议 服务端做从机
 *
 * <AUTHOR>
 * @date 2025/08/05 11:23
 */
@Slf4j
@Component
public class Dlt645TcpServerSlave implements CommandLineRunner {
    @Value("dlt645.port")
    private int port;

    /**
     * key存放equId(ip:port)，value存放ctx；保证线程安全使用ConcurrentHashMap
     */
    public static ConcurrentHashMap<String, ChannelHandlerContext> connectedEquId2Ctx = new ConcurrentHashMap<String, ChannelHandlerContext>();

    /**
     * boss 线程组，用于服务端接受客户端的连接
     */
    private EventLoopGroup bossGroup = new NioEventLoopGroup();

    /**
     * worker 线程组，用于服务端接受客户端的数据读写
     */
    private EventLoopGroup workerGroup = new NioEventLoopGroup();

    @Override
    public void run(String... args) throws Exception {
        ChannelFuture future;
        try {
            ServerBootstrap bootstrap = new ServerBootstrap();
            bootstrap.group(bossGroup, workerGroup)
                    .channel(NioServerSocketChannel.class)
                    .childHandler(new ChannelInitializer<SocketChannel>() {
                        @Override
                        protected void initChannel(SocketChannel sc) throws Exception {
                            sc.pipeline().addLast(new ServerHandler()); // 配置具体数据接收方法的处理
                        }
                    })
                    // 请求的队列的最大长度
                    .option(ChannelOption.SO_BACKLOG, 128)
                    // 保持长连接
                    .childOption(ChannelOption.SO_KEEPALIVE, true);
            // 绑定端口，开始接收进来的连接
            future = bootstrap.bind(port).sync();
            future.channel().closeFuture().sync();
        } catch (Exception e) {
            log.error("DLT645 Server Error: ", e);
        } finally {
            log.info("Finally Server Shutdown Gracefully");
            shutdown();
        }
    }

    /**
     * 关闭 Netty Server
     */
    @PreDestroy
    public void shutdown() {
        log.info("ShutDown DLT645 Server!");
        // 优雅关闭两个 EventLoopGroup 对象
        bossGroup.shutdownGracefully();
        workerGroup.shutdownGracefully();
    }
}
