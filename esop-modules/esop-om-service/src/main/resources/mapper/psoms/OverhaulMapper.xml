<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjwly.psoms.mapper.OverhaulMapper">
  <resultMap id="BaseResultMap" type="com.zjwly.psoms.domain.Overhaul">
    <!--@mbg.generated-->
    <!--@Table overhaul-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="equipment_id" jdbcType="INTEGER" property="equipmentId" />
    <result column="equipment_lot_no" jdbcType="VARCHAR" property="equipmentLotNo" />
    <result column="equipment_name" jdbcType="VARCHAR" property="equipmentName" />
    <result column="equipment_no" jdbcType="VARCHAR" property="equipmentNo" />
    <result column="capacity" jdbcType="DECIMAL" property="capacity" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="main_project" jdbcType="VARCHAR" property="mainProject" />
    <result column="start_date" jdbcType="DATE" property="startDate" />
    <result column="completion_date" jdbcType="DATE" property="completionDate" />
    <result column="days" jdbcType="INTEGER" property="days" />
    <result column="overrun_description" jdbcType="VARCHAR" property="overrunDescription" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_by" jdbcType="INTEGER" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="INTEGER" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, equipment_id, equipment_lot_no, equipment_name, equipment_no, capacity, `type`, 
    main_project, start_date, completion_date, `days`, overrun_description, remark, create_by, 
    create_time, update_by, update_time
  </sql>
</mapper>