<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjwly.psoms.mapper.DefectChangeLogMapper">
  <select id="listByDefectId" resultType="com.zjwly.psoms.domain.DefectChangeLog">
    SELECT dcl.*, su.nick_name AS createByName, fn.field_desc AS fieldDesc
    FROM om_defect_change_log dcl
           LEFT JOIN esop.sys_user su ON dcl.create_by = su.user_id
           LEFT JOIN om_field_name fn ON dcl.class_name = fn.class_name AND dcl.field_name = fn.field_name
    WHERE dcl.defect_id = #{id}
  </select>
</mapper>
