<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjwly.psoms.mapper.PeriodicPlanMapper">
    <sql id="listCondition">
        <if test="startTime != null and endTime != null">
            AND NOT (pp.end_date &lt; #{startTime} OR pp.start_date > #{endTime})
        </if>
        <if test="cycleType != null">
            AND pp.cycle_type = #{cycleType}
        </if>
        <if test="status != null">
            AND pp.status = #{status}
        </if>
        <if test="taskContent != null and taskContent != ''">
            AND pp.task_content LIKE "%"#{taskContent}"%"
        </if>
    </sql>
    <select id="listInfo" resultType="com.zjwly.psoms.domain.vo.PeriodicPlanVO">
        SELECT pp.*,
               su.nick_name AS createByName
        FROM om_periodic_plan pp
                 LEFT JOIN esop.sys_user su ON pp.create_by = su.user_id
        WHERE (pp.lot_no, pp.create_time) IN (SELECT lot_no, MAX(create_time)
                                              FROM om_periodic_plan
        <where>
            <include refid="listCondition"/>
        </where>
        GROUP BY lot_no)
        <include refid="listCondition"/>
        ORDER BY pp.id DESC
    </select>

    <select id="listEditRecord" resultType="com.zjwly.psoms.domain.vo.PeriodicPlanVO">
        SELECT pp.*,
               su.nick_name AS createByName
        FROM om_periodic_plan pp
                 LEFT JOIN esop.sys_user su ON pp.create_by = su.user_id
        WHERE pp.lot_no = #{lotNo}
    </select>

    <select id="listSchedulerData" resultType="com.zjwly.psoms.domain.PeriodicPlan">
        SELECT pp.*
        FROM om_periodic_plan pp
        WHERE start_date &lt;= #{startTime}
          AND #{endTime} &lt;= end_date
          AND status = #{status}
          AND (pp.lot_no, pp.create_time) IN
              (SELECT lot_no, MAX(create_time)
               FROM om_periodic_plan
               WHERE start_date &lt;= #{startTime}
                 AND #{endTime} &lt;= end_date
                 AND status = #{status}
               GROUP BY lot_no)
    </select>
</mapper>
