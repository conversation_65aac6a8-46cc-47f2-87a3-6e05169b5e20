<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjwly.psoms.mapper.EquipmentChangeMapper">
    <resultMap id="BaseResultMap" type="com.zjwly.psoms.domain.EquipmentChange">
        <!--@mbg.generated-->
        <!--@Table equipment_change-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="req_no" jdbcType="VARCHAR" property="reqNo"/>
        <result column="serial_no" jdbcType="INTEGER" property="serialNo"/>
        <result column="equipment_id" jdbcType="INTEGER" property="equipmentId"/>
        <result column="equipment_lot_no" jdbcType="VARCHAR" property="equipmentLotNo"/>
        <result column="equipment_name" jdbcType="VARCHAR" property="equipmentName"/>
        <result column="equipment_no" jdbcType="VARCHAR" property="equipmentNo"/>
        <result column="change_reason" jdbcType="VARCHAR" property="changeReason"/>
        <result column="applicant_unit" jdbcType="VARCHAR" property="applicantUnit"/>
        <result column="applicant" jdbcType="VARCHAR" property="applicant"/>
        <result column="applicant_time" jdbcType="TIMESTAMP" property="applicantTime"/>
        <result column="specialty" jdbcType="VARCHAR" property="specialty"/>
        <result column="before" jdbcType="VARCHAR" property="before"/>
        <result column="before_atts" jdbcType="VARCHAR" property="beforeAtts"/>
        <result column="after" jdbcType="VARCHAR" property="after"/>
        <result column="after_atts" jdbcType="VARCHAR" property="afterAtts"/>
        <result column="qa_req" jdbcType="VARCHAR" property="qaReq"/>
        <result column="qa_req_atts" jdbcType="VARCHAR" property="qaReqAtts"/>
        <result column="group1_req" jdbcType="VARCHAR" property="group1Req"/>
        <result column="group2_req" jdbcType="VARCHAR" property="group2Req"/>
        <result column="group3_req" jdbcType="VARCHAR" property="group3Req"/>
        <result column="group4_req" jdbcType="VARCHAR" property="group4Req"/>
        <result column="group5_req" jdbcType="VARCHAR" property="group5Req"/>
        <result column="group6_req" jdbcType="VARCHAR" property="group6Req"/>
        <result column="create_by_req" jdbcType="BIGINT" property="createByReq"/>
        <result column="create_time_req" jdbcType="TIMESTAMP" property="createTimeReq"/>
        <result column="update_by_req" jdbcType="BIGINT" property="updateByReq"/>
        <result column="update_time_req" jdbcType="TIMESTAMP" property="updateTimeReq"/>
        <result column="rep_no" jdbcType="VARCHAR" property="repNo"/>
        <result column="serial_no_rep" jdbcType="INTEGER" property="serialNoRep"/>
        <result column="bfr_rep" jdbcType="VARCHAR" property="bfrRep"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="completion_time" jdbcType="TIMESTAMP" property="completionTime"/>
        <result column="conclusion" jdbcType="VARCHAR" property="conclusion"/>
        <result column="reporter" jdbcType="BIGINT" property="reporter"/>
        <result column="report_time" jdbcType="TIMESTAMP" property="reportTime"/>
        <result column="manual" jdbcType="VARCHAR" property="manual"/>
        <result column="manual_atts" jdbcType="VARCHAR" property="manualAtts"/>
        <result column="le_ps_inv" jdbcType="VARCHAR" property="lePsInv"/>
        <result column="le_ps_inv_atts" jdbcType="VARCHAR" property="lePsInvAtts"/>
        <result column="st_sd" jdbcType="VARCHAR" property="stSd"/>
        <result column="st_sd_atts" jdbcType="VARCHAR" property="stSdAtts"/>
        <result column="other" jdbcType="VARCHAR" property="other"/>
        <result column="other_atts" jdbcType="VARCHAR" property="otherAtts"/>
        <result column="qa_rep" jdbcType="VARCHAR" property="qaRep"/>
        <result column="qa_rep_atts" jdbcType="VARCHAR" property="qaRepAtts"/>
        <result column="group1_rep" jdbcType="VARCHAR" property="group1Rep"/>
        <result column="group2_rep" jdbcType="VARCHAR" property="group2Rep"/>
        <result column="group3_rep" jdbcType="VARCHAR" property="group3Rep"/>
        <result column="group4_rep" jdbcType="VARCHAR" property="group4Rep"/>
        <result column="group5_rep" jdbcType="VARCHAR" property="group5Rep"/>
        <result column="create_by_rep" jdbcType="BIGINT" property="createByRep"/>
        <result column="create_time_rep" jdbcType="TIMESTAMP" property="createTimeRep"/>
        <result column="update_by_rep" jdbcType="BIGINT" property="updateByRep"/>
        <result column="update_time_rep" jdbcType="TIMESTAMP" property="updateTimeRep"/>
    </resultMap>

    <resultMap id="VoResultMap" type="com.zjwly.psoms.domain.vo.EquipmentChangeVO" extends="BaseResultMap">
        <collection property="beforeAttachments" ofType="com.zjwly.psoms.domain.Attachment" select="listAtt"
                    column="before_atts"/>
        <collection property="afterAttachments" ofType="com.zjwly.psoms.domain.Attachment" select="listAtt"
                    column="after_atts"/>
        <collection property="qaReqAttachments" ofType="com.zjwly.psoms.domain.Attachment" select="listAtt"
                    column="qa_req_atts"/>
        <collection property="manualAttachments" ofType="com.zjwly.psoms.domain.Attachment" select="listAtt"
                    column="manual_atts"/>
        <collection property="lePsInvAttachments" ofType="com.zjwly.psoms.domain.Attachment" select="listAtt"
                    column="le_ps_inv_atts"/>
        <collection property="stSdAttachments" ofType="com.zjwly.psoms.domain.Attachment" select="listAtt"
                    column="st_sd_atts"/>
        <collection property="otherAttachments" ofType="com.zjwly.psoms.domain.Attachment" select="listAtt"
                    column="other_atts"/>
        <collection property="qaRepAttachments" ofType="com.zjwly.psoms.domain.Attachment" select="listAtt"
                    column="qa_rep_atts"/>

        <collection property="group1ReqIdNames" ofType="com.zjwly.psoms.domain.vo.IdNameTimeVO" select="listUserIdName"
                    column="{orderId=orderIdReq,group=group1_req}"/>
        <collection property="group2ReqIdNames" ofType="com.zjwly.psoms.domain.vo.IdNameTimeVO" select="listUserIdName"
                    column="{orderId=orderIdReq,group=group2_req}"/>
        <collection property="group3ReqIdNames" ofType="com.zjwly.psoms.domain.vo.IdNameTimeVO" select="listUserIdName"
                    column="{orderId=orderIdReq,group=group3_req}"/>
        <collection property="group4ReqIdNames" ofType="com.zjwly.psoms.domain.vo.IdNameTimeVO" select="listUserIdName"
                    column="{orderId=orderIdReq,group=group4_req}"/>
        <collection property="group5ReqIdNames" ofType="com.zjwly.psoms.domain.vo.IdNameTimeVO" select="listUserIdName"
                    column="{orderId=orderIdReq,group=group5_req}"/>

        <collection property="group1RepIdNames" ofType="com.zjwly.psoms.domain.vo.IdNameTimeVO" select="listUserIdName"
                    column="{orderId=orderIdRep,group=group1_rep}"/>
        <collection property="group2RepIdNames" ofType="com.zjwly.psoms.domain.vo.IdNameTimeVO" select="listUserIdName"
                    column="{orderId=orderIdRep,group=group2_rep}"/>
        <collection property="group3RepIdNames" ofType="com.zjwly.psoms.domain.vo.IdNameTimeVO" select="listUserIdName"
                    column="{orderId=orderIdRep,group=group3_rep}"/>
        <collection property="group4RepIdNames" ofType="com.zjwly.psoms.domain.vo.IdNameTimeVO" select="listUserIdName"
                    column="{orderId=orderIdRep,group=group4_rep}"/>
        <collection property="group5RepIdNames" ofType="com.zjwly.psoms.domain.vo.IdNameTimeVO" select="listUserIdName"
                    column="{orderId=orderIdRep,group=group5_rep}"/>
    </resultMap>

    <select id="listAtt" resultType="com.zjwly.psoms.domain.Attachment">
        SELECT *
        FROM om_attachment
        WHERE FIND_IN_SET(id, #{attachment_ids})
    </select>

    <select id="listUserIdName" resultType="com.zjwly.psoms.domain.vo.IdNameTimeVO">
        SELECT su.user_id AS id, su.nick_name AS name
        FROM esop.sys_user su
                 LEFT JOIN wf_hist_task wht ON wht.operator = su.user_id
        WHERE FIND_IN_SET(su.user_id, #{group})
          AND wht.order_Id = #{orderId}
          AND wht.finish_Time > (SELECT t.create_time
                                 FROM (SELECT create_time
                                       FROM wf_hist_task
                                       WHERE display_Name = '发起申请'
                                         AND order_Id = #{orderId}
                                       UNION
                                       SELECT create_time
                                       FROM wf_task
                                       WHERE display_Name = '发起申请'
                                         AND order_Id = #{orderId}) t
                                 ORDER BY t.create_time DESC
            LIMIT 1)
        GROUP BY su.user_id
    </select>

    <select id="getDetail" resultMap="VoResultMap">
        SELECT ec.*,
               eo1.order_id AS orderIdReq,
               eo1.status   AS statusReq,
               eo2.order_id AS orderIdRep,
               eo2.status   AS statusRep,
               su.nick_name AS reporterName
        FROM om_equipment_change ec
                 LEFT JOIN ext_operation eo1
                           ON eo1.business_Id = ec.id AND eo1.business_Type = 'EQUIPMENT_CHANGE_REQUEST_FORM'
                 LEFT JOIN ext_operation eo2
                           ON eo2.business_Id = ec.id AND eo2.business_Type = 'PRODUCTION_EQUIPMENT_CHANGE_REPORT_FORM'
                 LEFT JOIN esop.sys_user su ON su.user_id = ec.reporter
        WHERE ec.id = #{id}
        ORDER BY eo1.create_time DESC, eo2.create_time DESC
        LIMIT 1
    </select>

    <select id="listInfo" resultMap="VoResultMap">
        WITH CTE AS (SELECT ec.*,
                            eo1.order_id                                                                                                                                                           AS orderIdReq,
                            eo1.STATUS                                                                                                                                                             AS statusReq,
                            su1.user_id                                                                                                                                                            AS auditCreateByReq,
                            wt1.display_Name                                                                                                                                                       AS displayNameReq,
                            eo2.order_id                                                                                                                                                           AS orderIdRep,
                            eo2.STATUS                                                                                                                                                             AS statusRep,
                            su2.user_id                                                                                                                                                            AS auditCreateByRep,
                            wt2.display_Name                                                                                                                                                       AS displayNameRep,
                            ROW_NUMBER() OVER ( PARTITION BY eo1.order_id, wt1.order_id, eo2.order_id, wt2.order_id ORDER BY eo1.id DESC, wt1.create_time DESC,eo2.id DESC, wt2.create_time DESC ) AS rn
                     FROM om_equipment_change ec
                              LEFT JOIN ext_operation eo1 ON eo1.business_Id = ec.id
                         AND eo1.business_Type = 'EQUIPMENT_CHANGE_REQUEST_FORM'
                              LEFT JOIN wf_task wt1 ON wt1.order_Id = eo1.order_id
                              LEFT JOIN esop.sys_user su1 ON eo1.create_by = su1.user_name
                              LEFT JOIN ext_operation eo2 ON eo2.business_Id = ec.id
                         AND eo2.business_Type = 'PRODUCTION_EQUIPMENT_CHANGE_REPORT_FORM'
                              LEFT JOIN wf_task wt2 ON wt2.order_Id = eo2.order_id
                              LEFT JOIN esop.sys_user su2 ON eo2.create_by = su2.user_name)
        SELECT *
        FROM CTE
        WHERE (rn = 1 OR orderIdReq IS NULL)
        <if test="reqNo != null and reqNo != ''">
            AND serial_no LIKE "%"#{reqNo}"%"
        </if>
        <if test="equipmentName != null and equipmentName != ''">
              AND equipment_name LIKE "%"#{equipmentName}"%"
        </if>
        <if test="applicantUnit != null and applicantUnit != ''">
            AND applicant_unit LIKE "%"#{applicantUnit}"%"
        </if>
        <if test="applicant != null and applicant != ''">
            AND applicant LIKE "%"#{applicant}"%"
        </if>
        ORDER BY id DESC
    </select>

    <select id="getApprovingCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM ext_operation eo
                 JOIN esop.sys_user su ON eo.create_by = su.user_name
        WHERE (eo.business_Type = 'EQUIPMENT_CHANGE_REQUEST_FORM'
            OR eo.business_Type = 'PRODUCTION_EQUIPMENT_CHANGE_REPORT_FORM')
          AND eo.status = 'APPROVING'
        <if test="startTime != null and endTime != null">
            AND eo.create_time >= #{startTime}
            AND eo.create_time &lt; #{endTime}
        </if>
        <if test="userId != null">
            AND su.user_id = #{userId}
        </if>
    </select>

    <select id="getLastTaskOperator" resultType="com.zjwly.psoms.domain.vo.IdNameTimeVO">
        SELECT su.user_id AS id, su.nick_name AS name, wht.finish_Time AS time
        FROM wf_hist_task wht
                 LEFT JOIN esop.sys_user su ON su.user_id = wht.operator
        WHERE wht.order_Id = #{orderId}
        ORDER BY wht.create_Time DESC, wht.finish_Time DESC
        LIMIT 1
    </select>

    <select id="getExtOperationByOrderId" resultType="map">
        SELECT eo.business_Id AS businessId, eo.business_Type AS businessType
        FROM ext_operation eo
        WHERE eo.order_id = #{orderId}
        ORDER BY eo.id DESC
        LIMIT 1
    </select>

    <select id="getGroup" resultType="com.zjwly.common.vo.IdNameVO">
        SELECT su.user_id AS id, su.nick_name AS name
        FROM esop.sys_user su
        WHERE FIND_IN_SET(su.user_id, #{group})
    </select>
</mapper>
