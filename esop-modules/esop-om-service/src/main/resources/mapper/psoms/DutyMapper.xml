<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjwly.psoms.mapper.DutyMapper">

    <select id="listInfo" resultType="com.zjwly.psoms.domain.vo.DutyVO">
        SELECT
            d.*,
            ( SELECT GROUP_CONCAT( u1.nick_name ) FROM esop.sys_user u1 WHERE FIND_IN_SET( u1.user_id, d.duty1 ) ) AS duty1Name,
            ( SELECT GROUP_CONCAT( u2.nick_name ) FROM esop.sys_user u2 WHERE FIND_IN_SET( u2.user_id, d.duty2 ) ) AS duty2Name,
            ( SELECT GROUP_CONCAT( u3.nick_name ) FROM esop.sys_user u3 WHERE FIND_IN_SET( u3.user_id, d.duty3 ) ) AS duty3Name,
            ( SELECT GROUP_CONCAT( u4.nick_name ) FROM esop.sys_user u4 WHERE FIND_IN_SET( u4.user_id, d.duty4 ) ) AS duty4Name,
            ( SELECT GROUP_CONCAT( u5.nick_name ) FROM esop.sys_user u5 WHERE FIND_IN_SET( u5.user_id, d.duty5 ) ) AS duty5Name
        FROM
        om_duty d
        <where>
            <if test="startTime != null and startTime != ''">
                d.effective_time >= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                AND d.effective_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY d.id DESC
    </select>
</mapper>
