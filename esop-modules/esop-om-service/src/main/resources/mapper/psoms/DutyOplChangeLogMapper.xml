<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjwly.psoms.mapper.DutyOplChangeLogMapper">
    <select id="listByLotNo" resultType="com.zjwly.psoms.domain.vo.DutyOplChangeLogVO">
        SELECT docl.*, su.nick_name AS createByName, fn.field_desc AS fieldDesc
        FROM om_duty_opl_change_log docl
                 LEFT JOIN esop.sys_user su ON docl.create_by = su.user_id
        LEFT JOIN om_field_name fn ON docl.class_name = fn.class_name AND docl.field_name = fn.field_name
        WHERE docl.oplog_lot_no = #{lotNo}
    </select>
</mapper>
