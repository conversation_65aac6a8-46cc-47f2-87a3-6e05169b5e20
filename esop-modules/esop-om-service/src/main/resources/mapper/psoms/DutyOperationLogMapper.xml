<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjwly.psoms.mapper.DutyOperationLogMapper">
    <sql id="listCondition">
        <if test="startTime != null and startTime != ''">
            AND dol.op_time >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND dol.op_time &lt;= #{endTime}
        </if>
        <if test="type != null">
            AND dol.op_type = #{type}
        </if>
        <if test="handoverDuty != null">
            AND dol.handover_duty = #{handoverDuty}
        </if>
        <if test="handoverName != null and handoverName != ''">
            AND su1.nick_name LIKE "%"#{handoverName}"%"
        </if>
        <if test="takeoverDuty != null">
            AND dol.takeover_duty = #{takeoverDuty}
        </if>
        <if test="status != null">
            AND dol.op_status = #{status}
        </if>
        <if test="takeoverName != null and takeoverName != ''">
            AND su2.nick_name LIKE "%"#{takeoverName}"%"
        </if>
    </sql>
    <select id="listPage" resultType="com.zjwly.psoms.domain.vo.DutyOperationLogVO">
        SELECT dol.*, su1.nick_name AS handoverName, su2.nick_name AS takeoverName
        FROM om_duty_operation_log dol
                 LEFT JOIN esop.sys_user su1 ON dol.handover_by = su1.user_id
                 LEFT JOIN esop.sys_user su2 ON dol.takeover_by = su2.user_id
        <where>
            (dol.lot_no, dol.version) IN (
            SELECT lot_no, MAX(version)
            FROM `om_duty_operation_log`
            <where>
                <include refid="listCondition"/>
            </where>
              GROUP BY lot_no
            )
            <include refid="listCondition"/>
        </where>
        ORDER BY dol.id DESC
    </select>

    <select id="listByLotNo" resultType="com.zjwly.psoms.domain.vo.DutyOperationLogVO">
        SELECT dol.*, su1.nick_name AS handoverName, su2.nick_name AS takeoverName, su3.nick_name AS createByName
        FROM om_duty_operation_log dol
                 LEFT JOIN esop.sys_user su1 ON dol.handover_by = su1.user_id
                 LEFT JOIN esop.sys_user su2 ON dol.takeover_by = su2.user_id
                 LEFT JOIN esop.sys_user su3 ON dol.create_by = su3.user_id
        WHERE dol.lot_no = #{lotNo}
    </select>

    <select id="listVersion" resultType="com.zjwly.psoms.domain.vo.DutyOpVersionVO">
        SELECT id AS opId, version FROM om_duty_operation_log dol WHERE dol.lot_no = #{lotNo}
    </select>

    <select id="detail" resultType="com.zjwly.psoms.domain.vo.DutyOperationLogVO">
        SELECT dol.*, su1.nick_name AS handoverName, su2.nick_name AS takeoverName
        FROM om_duty_operation_log dol
                 LEFT JOIN esop.sys_user su1 ON dol.handover_by = su1.user_id
                 LEFT JOIN esop.sys_user su2 ON dol.takeover_by = su2.user_id
        WHERE dol.id = #{id}
    </select>

    <select id="listCalendar" resultType="com.zjwly.psoms.domain.vo.DutyCalendarVO">
        SELECT
            op_time AS date,
            MAX(CASE WHEN op_type = 0 THEN op_status END) AS b,
            MAX(CASE WHEN op_type = 1 THEN op_status END) AS z,
            MAX(CASE WHEN op_type = 2 THEN op_status END) AS y
        FROM
        om_duty_operation_log
        WHERE
            op_time >= #{startTime} AND op_time &lt;= #{endTime}
        GROUP BY
            op_time
        ORDER BY
            op_time
    </select>
</mapper>
