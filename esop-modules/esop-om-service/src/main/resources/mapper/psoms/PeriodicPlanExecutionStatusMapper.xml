<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjwly.psoms.mapper.PeriodicPlanExecutionStatusMapper">

    <select id="listInfo" resultType="com.zjwly.psoms.domain.vo.PeriodicPlanExecutionStatusVO">
        SELECT ppes.*, su.nick_name AS executorName
        FROM om_periodic_plan_execution_status ppes
                 LEFT JOIN esop.sys_user su ON ppes.executor_id = su.user_id
        <where>
            <if test="cycleType != null">
                ppes.cycle_type = #{cycleType}
            </if>
            <if test="startDate != null and startDate != ''">
                AND ppes.start_date >= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND ppes.end_date &lt;= #{endDate}
            </if>
            <if test="shiftType != null">
                AND ppes.shift_type = #{shiftType}
            </if>
            <if test="status != null">
                AND ppes.status = #{status}
            </if>
            <if test="executorId != null">
                AND ppes.executor_id = #{executorId}
            </if>
            <if test="taskContent != null and taskContent != ''">
                AND ppes.task_content LIKE "%"#{taskContent}"%"
            </if>
        </where>
        ORDER BY ppes.id DESC
    </select>

    <select id="countByPpIdAndDate" resultType="int">
        select count(*) from om_periodic_plan_execution_status where pp_id = #{ppId} and start_date &lt;= #{startTime} AND #{endTime} &lt;= end_date
    </select>
</mapper>
