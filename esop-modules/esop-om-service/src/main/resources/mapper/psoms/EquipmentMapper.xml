<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjwly.psoms.mapper.EquipmentMapper">
    <resultMap id="BaseResultMap" type="com.zjwly.psoms.domain.Equipment">
        <!--@mbg.generated-->
        <!--@Table equipment-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="lot_no" jdbcType="VARCHAR" property="lotNo"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="no" jdbcType="VARCHAR" property="no"/>
        <result column="spec_model" jdbcType="VARCHAR" property="specModel"/>
        <result column="param" jdbcType="VARCHAR" property="param"/>
        <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer"/>
        <result column="arrival_date" jdbcType="DATE" property="arrivalDate"/>
        <result column="usage_date" jdbcType="DATE" property="usageDate"/>
        <result column="location" jdbcType="VARCHAR" property="location"/>
        <result column="create_by" jdbcType="INTEGER" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="INTEGER" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="attachment_ids" jdbcType="VARCHAR" property="attachmentIds"/>
        <result column="process_type" jdbcType="VARCHAR" property="processType"/>
    </resultMap>

    <resultMap id="VoResultMap" type="com.zjwly.psoms.domain.vo.EquipmentVO" extends="BaseResultMap">
        <result column="order_id" property="orderId"/>
        <result column="status" property="status"/>
        <result column="type" property="type"/>
        <result column="displayName" property="displayName"/>
        <result column="createByName" property="createByName"/>
        <result column="auditCreateBy" property="auditCreateBy"/>
        <collection property="attachments" ofType="com.zjwly.psoms.domain.Attachment" select="listAtt"
                    column="attachment_ids"/>
    </resultMap>

    <select id="listAtt" resultType="com.zjwly.psoms.domain.Attachment">
        SELECT *
        FROM om_attachment
        WHERE FIND_IN_SET(id, #{attachment_ids})
    </select>

    <select id="listOpArea" resultMap="VoResultMap">
        SELECT t.* FROM (
        SELECT e.*,
               eo.order_id,
               eo.`status`,
               IFNULL(eo.business_Type, e.process_type)                      AS type,
               wt.display_Name                                               AS displayName,
               su.nick_name                                                  AS createByName,
               su.user_id                                                    AS auditCreateBy,
               ROW_NUMBER() OVER (PARTITION BY e.lot_no ORDER BY eo.id DESC) AS rn
        FROM om_equipment e
                LEFT JOIN ext_operation eo ON eo.business_Id = e.id AND (eo.business_Type = 'OMS_CREATE' OR
                eo.business_Type = 'OMS_UPDATE' OR
                eo.business_Type = 'OMS_PHASEOUT')
                LEFT JOIN wf_task wt ON wt.order_Id = eo.order_id
                LEFT JOIN esop.sys_user su ON eo.create_by = su.user_id
        <where>
            (eo.`status` IS NULL OR eo.status != 'APPROVED')
              AND e.id IN (SELECT MAX(id) FROM om_equipment GROUP BY lot_no)
            <if test="name != null and name != ''">
                AND e.name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="approvalStatus != null and approvalStatus != ''">
                AND eo.status = #{approvalStatus}
            </if>
            <if test="no != null and no != ''">
                AND e.no LIKE CONCAT('%', #{no}, '%')
            </if>
            <if test="type != null and type != ''">
                AND eo.business_Type = #{type}
            </if>
        </where>
        ) t
        WHERE t.rn = 1
    </select>

    <select id="listInfo" resultMap="VoResultMap">
        SELECT t.* FROM (SELECT e.*,
                                eo.status,
                                IFNULL(eo.`business_Type`, e.process_type)                               AS type,
                                su.nick_name                                                             AS createByName,
                                su.user_id                                                               AS auditCreateBy,
                                ROW_NUMBER() OVER (PARTITION BY e.lot_no ORDER BY e.id DESC, eo.id DESC) AS row_num
                         FROM om_equipment e
                            JOIN ext_operation eo
                            ON eo.business_Id = e.id AND eo.STATUS = 'APPROVED' AND
                            (eo.business_Type = 'OMS_CREATE' OR eo.business_Type = 'OMS_UPDATE' OR
                            eo.business_Type = 'OMS_PHASEOUT')
                                  LEFT JOIN esop.sys_user su ON e.create_by = su.user_name
        <where>
            <if test="name != null and name != ''">
                e.name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="no != null and no != ''">
                AND e.no LIKE CONCAT('%', #{no}, '%')
            </if>
            <if test="type != null and type != ''">
                AND eo.business_Type = #{type}
            </if>
        </where>
        ORDER BY e.id) t
        WHERE t.row_num = 1
        ORDER BY t.id DESC
    </select>

    <select id="listVersion" resultType="com.zjwly.psoms.domain.vo.EquipmentVersionVO">
        SELECT e.id, ROW_NUMBER() OVER (ORDER BY e.id) version, eo.business_Type AS type
        FROM om_equipment e
                 JOIN ext_operation eo
                      ON eo.business_Id = e.id AND (eo.business_Type = 'OMS_CREATE' OR
                                                    eo.business_Type = 'OMS_UPDATE' OR
                                                    eo.business_Type = 'OMS_PHASEOUT')
        WHERE eo.STATUS = 'APPROVED'
          AND lot_no = #{lotNo} ORDER BY eo.id
    </select>

    <select id="getDetail" resultMap="VoResultMap">
        SELECT t.*
        FROM (SELECT ROW_NUMBER() OVER (PARTITION BY e.lot_no ORDER BY eo.id DESC) AS rn,
                     e.*,
                     eo.status,
                     eo.business_type                                              AS type
              FROM om_equipment e
                       LEFT JOIN ext_operation eo
                                 ON eo.business_Id = e.id AND (eo.business_Type = 'OMS_CREATE' OR
                                                               eo.business_Type = 'OMS_UPDATE' OR
                                                               eo.business_Type = 'OMS_PHASEOUT')
        WHERE e.id = #{id}
        <if test="status != null and status != ''">
            AND eo.STATUS = #{status}
        </if>
        <if test="type != null and type != ''">
            AND eo.business_Type = #{type}
        </if>
        ) t
        WHERE t.rn = 1
    </select>

    <select id="listApprovedInfo" resultMap="VoResultMap">
        SELECT * FROM (
        SELECT e.*,
               IFNULL(eo.`business_Type`, e.process_type)                      AS type,
               su.nick_name                                                    AS createByName,
               su.user_id                                                      AS auditCreateBy,
               ROW_NUMBER() OVER ( PARTITION BY e.lot_no ORDER BY eo.id DESC ) AS rn
        FROM om_equipment e
                 JOIN ext_operation eo ON eo.business_Id = e.id AND (eo.business_Type = 'OMS_CREATE' OR
                                                                            eo.business_Type = 'OMS_UPDATE' OR
                                                                            eo.business_Type = 'OMS_PHASEOUT')
                 LEFT JOIN esop.sys_user su ON eo.create_by = su.user_name
        WHERE eo.status = 'APPROVED'
        <if test="name != null and name != ''">
            AND e.name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="no != null and no != ''">
            AND e.no LIKE CONCAT('%', #{no}, '%')
        </if>) t
    WHERE t.rn = 1
      AND t.type != 'DZ_OMS_PHASEOUT'
    </select>

    <select id="listApprovedIds" resultType="java.lang.Integer">
        SELECT DISTINCT e.id
        FROM om_equipment e
                 JOIN ext_operation eo
                      ON eo.business_Id = e.id AND
                         (eo.business_Type = 'OMS_CREATE' OR eo.business_Type = 'OMS_UPDATE' OR
                          eo.business_Type = 'OMS_PHASEOUT')
        WHERE eo.STATUS = 'APPROVED'
          AND lot_no = #{lotNo}
    </select>

    <select id="getApprovingCount" resultType="int">
        SELECT COUNT(*)
        FROM ext_operation eo
                 JOIN esop.sys_user su ON eo.create_by = su.user_name
        WHERE (eo.business_Type = 'OMS_CREATE'
            OR eo.business_Type = 'OMS_UPDATE'
            OR eo.business_Type = 'OMS_PHASEOUT')
          AND eo.status = 'APPROVING'
        <if test="startTime != null and endTime != null">
            AND eo.create_time >= #{startTime}
            AND eo.create_time &lt; #{endTime}
        </if>
        <if test="userId != null">
            AND su.user_id = #{userId}
        </if>
    </select>
</mapper>
