<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjwly.psoms.mapper.DefectMapper">
    <select id="getDetail" resultType="com.zjwly.psoms.domain.vo.DefectVO">
        SELECT d.*, su.nick_name AS createByName
        FROM om_defect d
                 LEFT JOIN esop.sys_user su ON d.create_by = su.user_id
        WHERE d.id = #{id}
    </select>
</mapper>
