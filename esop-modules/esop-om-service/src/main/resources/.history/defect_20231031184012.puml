@startuml
hide empty description
left to right direction
[*] --> 草稿 : 开始

草稿 --> 草稿 : 修改(1)
草稿 --> 缺陷录入 : 提交(2)
草稿 --> 草稿 : 删除

缺陷录入 --> 缺陷下达 : 确认(3)
缺陷录入 --> 工单取消申请 : 取消(8)
工单取消申请 --> 缺陷录入 : (驳回: 申请前上一步状态)

缺陷下达 --> 工作安排 : 分发(4)
缺陷下达 --> 工单取消申请 : 取消(8)
工单取消申请 --> 缺陷下达 : (驳回: 申请前上一步状态)
缺陷下达 --> 延期申请 : 延期申请(9)
延期申请 --> 缺陷下达 : 撤回(申请前上一步状态)
延期申请 --> 缺陷下达 : 驳回: 申请前上一步状态

工作安排 --> 工作完成 : 完成(5)
工作安排 --> 工单取消申请 : 取消(8)
工单取消申请 --> 工作安排 : (驳回: 申请前上一步状态)
工作安排 --> 延期申请 : 延期申请(9)
延期申请 --> 工作安排 : 撤回(申请前上一步状态)
延期申请 --> 工作安排 : 驳回: 申请前上一步状态

工作完成 --> 验收合格 : 验收(6)
工作完成 --> 工单取消申请 : 取消(8)
工单取消申请 --> 工作完成 : (驳回: 申请前上一步状态)
工作完成 --> 延期申请 : 延期申请(9)
延期申请 --> 工作完成 : 撤回(申请前上一步状态)
延期申请 --> 工作完成 : 驳回: 申请前上一步状态

验收合格 --> 缺陷关闭 : 关闭(7)
验收合格 --> 工单取消申请 : 取消(8)
工单取消申请 --> 验收合格 : (驳回: 申请前上一步状态)
验收合格 --> 延期申请 : 延期申请(9)
延期申请 --> 验收合格 : 撤回(申请前上一步状态)
延期申请 --> 验收合格 : 驳回: 申请前上一步状态

延期申请 --> 延期通过 : 延期审核(通过: 3)
工单取消申请 --> 工单取消 : (通过: 10)
缺陷关闭 --> [*]
工单取消 --> [*]
延期通过 --> [*]

' state 延期{
'  [*] --> 延期申请
'  延期申请 --> 延期通过 : 延期审核(通过: 3)
' '  延期申请 --> 上一步状态 : 撤回(申请前上一步状态)
' '  延期申请 --> 上一步状态 : 驳回: 申请前上一步状态
'  延期通过 --> [*]
' '  上一步状态 --> [*]
' }


' state 取消{
'  [*] --> 工单取消申请
'  工单取消申请 --> 工单取消 : (通过: 10)
' '  工单取消申请 --> 申请前上一步状态 : (驳回: 申请前上一步状态)
'  工单取消 --> [*]
' '  申请前上一步状态 --> [*]
' } 
 

@enduml
