package com.zjwly.psoms.controller;

import cn.hutool.core.codec.Base64Encoder;
import cn.hutool.core.io.IoUtil;
import com.zjwly.common.base.Result;
import com.zjwly.common.exception.ServiceException;
import com.zjwly.common.web.utils.FastDfsUtil;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;

/**
 * 文件服务
 *
 * <AUTHOR>
 * @date 2023/11/21 14:49
 */
@RequiredArgsConstructor
@RequestMapping("/ds/om/file")
@RestController
public class FastDfsController {
    private final FastDfsUtil fastDfsUtil;

    /**
     * 下载文件
     *
     * @param filePath 附件路径
     * @param response
     * @return void
     * <AUTHOR>
     * @date 2023/11/21 14:51
     */
    @GetMapping("/download")
    public void downloadFile(@RequestParam("filePath") String filePath, HttpServletResponse response) {
        ServletOutputStream outputStream = null;
        try {
            byte[] bytes = fastDfsUtil.downloadByteArray(filePath);
            if (bytes != null) {
                outputStream = response.getOutputStream();
                outputStream.write(bytes);
                outputStream.flush();
            }
        } catch (IOException e) {
            throw new ServiceException("下载文件输出流异常！", e);
        } finally {
            IoUtil.close(outputStream);
        }
    }

    /**
     * 返回图片 base64
     *
     * @param filePath 图片路径
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/11/24 14:05
     */
    @GetMapping("/getPictureBase64")
    public Result getPictureBase64(@RequestParam("filePath") String filePath) {
        String base64 = null;
        byte[] bytes = fastDfsUtil.downloadByteArray(filePath);
        if (bytes != null) {
            base64 = Base64Encoder.encode(bytes);
        }
        return Result.ok("data:image/gif;base64," + base64);
    }

}
