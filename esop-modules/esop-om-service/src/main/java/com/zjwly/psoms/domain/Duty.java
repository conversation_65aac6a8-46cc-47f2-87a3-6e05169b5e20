package com.zjwly.psoms.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zjwly.common.mybatis.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 值班安排
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName(value = "om_duty")
public class Duty extends BaseEntity {
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 生效时间
     */
    @TableField(value = "effective_time")
    private Date effectiveTime;

    /**
     * 失效时间
     */
    @TableField(value = "expiration_time")
    private Date expirationTime;

    /**
     * 一值
     */
    @TableField(value = "duty1")
    private String duty1;

    /**
     * 二值
     */
    @TableField(value = "duty2")
    private String duty2;

    /**
     * 三值
     */
    @TableField(value = "duty3")
    private String duty3;

    /**
     * 四值
     */
    @TableField(value = "duty4")
    private String duty4;

    /**
     * 五值
     */
    @TableField(value = "duty5")
    private String duty5;

    /**
     * 启用状态 0：停用，1：启用
     */
    @TableField(value = "enabled")
    private Integer enabled;


}
