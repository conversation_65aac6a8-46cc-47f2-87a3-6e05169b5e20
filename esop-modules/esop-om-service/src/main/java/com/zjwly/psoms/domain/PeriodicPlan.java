package com.zjwly.psoms.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zjwly.common.mybatis.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 定期计划
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "om_periodic_plan")
@EqualsAndHashCode(callSuper = true)
public class PeriodicPlan extends BaseEntity {
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 批号
     */
    @TableField(value = "lot_no")
    private String lotNo;

    /**
     * 开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @TableField(value = "start_date")
    private Date startDate;

    /**
     * 结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @TableField(value = "end_date")
    private Date endDate;

    /**
     * 任务内容
     */
    @TableField(value = "task_content")
    private String taskContent;

    /**
     * 周期类型 0：班，1：日，2：周，3：月，4：季度，5：年
     */
    @TableField(value = "cycle_type")
    private Integer cycleType;

    /**
     * 周期类型为周时，第几周序号
     */
    @TableField(value = "week_no")
    private Integer weekNo;

    /**
     * 状态 0：未下发，1：已下发，2：已执行
     */
    @TableField(value = "`status`")
    private Integer status;

    /**
     * 下发时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date issueTime;

    /**
     * 记录类型 0：创建，1：变更
     */
    @TableField(value = "`type`")
    private Integer type;

}
