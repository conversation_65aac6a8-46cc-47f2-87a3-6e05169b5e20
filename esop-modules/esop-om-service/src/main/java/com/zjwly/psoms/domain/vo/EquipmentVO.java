package com.zjwly.psoms.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zjwly.psoms.converter.ApprovalTypeConverter;
import com.zjwly.psoms.domain.Attachment;
import com.zjwly.psoms.domain.Equipment;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 设备台账VO
 *
 * <AUTHOR>
 * @date 2023/10/25 14:45
 */
@ExcelIgnoreUnannotated
@EqualsAndHashCode(callSuper = true)
@Data
public class EquipmentVO extends Equipment {
    /**
     * 流程OrderId
     */
    private String orderId;

    /**
     * 审批状态
     * UNCOMMITTED("未 提交"),
     * APPROVING("审批中"),
     * APPROVED("审批完成"),
     * REJECTED("已驳回"),
     * CANCELED("已销"),
     * OBSOLETE("已废除")
     */
    private String status;

    /**
     * 审批类型：DZ_OMS_CREATE,DZ_OMS_UPDATE,DZ_OMS_PHASEOUT
     */
    @ExcelProperty(value = "状态", converter = ApprovalTypeConverter.class)
    private String type;

    /**
     * 审批节点
     */
    private String displayName;

    /**
     * 创建人
     */
    private String createByName;

    /**
     * 附件
     */
    private List<Attachment> attachments;

    /**
     * 审批发起人
     */
    private Long auditCreateBy;
}
