package com.zjwly.psoms.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 缺陷管理操作枚举
 *
 * <AUTHOR>
 * @date 2023/11/22 10:37
 */
@AllArgsConstructor
@Getter
public enum DefectOpEnum {
    SAVE(0, "新增"),
    UPDATE(1, "修改"),
    SUBMIT(2, "提交"),
    CONFIRM(3, "确认"),
    DISTRIBUTE(4, "分发"),
    COMPLETE(5, "完成"),
    ACCEPTANCE(6, "验收"),
    CLOSE(7, "关闭"),
    EXTENSION_REQUEST(8, "延期申请"),
    WITHDRAW(9, "撤回"),
    EXTENSION_PASS(10, "延期审核通过"),
    EXTENSION_REJECT(11, "延期审核驳回"),
    CANCEL(12, "取消"),
    CANCEL_PASSW(13, "取消审核通过"),
    CANCEL_REJECT(14, "取消审核驳回"),
    DELETE(15, "删除");

    @EnumValue
    private int code;
    private String name;

    private static final Map<Integer, DefectStateEnum> CACHE;
    static {
        CACHE = Arrays.stream(DefectStateEnum.values())
                      .collect(Collectors.toMap(DefectStateEnum::getCode, Function.identity()));
    }

    public static DefectStateEnum getByCode(Integer code) {
        return CACHE.get(code);
    }

}
