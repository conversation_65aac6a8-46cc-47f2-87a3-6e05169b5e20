package com.zjwly.psoms.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zjwly.psoms.domain.Defect;
import com.zjwly.psoms.domain.DefectChangeLog;
import com.zjwly.psoms.domain.vo.ChangeLogVO;

import java.util.List;

public interface DefectService extends IService<Defect> {

    /**
     * 修改
     *
     * @param defect
     * @return void
     * <AUTHOR>
     * @date 2023/11/01 10:45
     */
    void updateInfo(Defect defect);

    /**
     * 新增
     *
     * @param defect
     * @return void
     * <AUTHOR>
     * @date 2023/11/01 15:35
     */
    void saveInfo(Defect defect);

    IPage<Defect> listInfo(String name, String status, Long current, Long size);

    /**
     * 根据缺陷ID，获取修改日志
     *
     * @param id
     * @return java.util.List<com.zjwly.psoms.domain.vo.ChangeLogVO>
     * <AUTHOR>
     * @date 2023/11/07 15:47
     */
    List<ChangeLogVO<DefectChangeLog>> getChangeLog(Integer id);

    /**
     * 删除
     *
     * @param id
     * @return void
     * <AUTHOR>
     * @date 2023/11/22 14:44
     */
    void removeInfo(Integer id);

    /**
     * 提交
     *
     * @param defect
     * @return void
     * <AUTHOR>
     * @date 2023/11/22 14:45
     */
    void submit(Defect defect);

    /**
     * 确认
     *
     * @param defect
     * @return void
     * <AUTHOR>
     * @date 2023/11/22 14:45
     */
    void confirm(Defect defect);

    /**
     * 分发
     *
     * @param defect
     * @return void
     * <AUTHOR>
     * @date 2023/11/22 14:45
     */
    void distribute(Defect defect);

    /**
     * 完成
     *
     * @param defect
     * @return void
     * <AUTHOR>
     * @date 2023/11/22 14:45
     */
    void complete(Defect defect);

    /**
     * 验收
     *
     * @param defect
     * @return void
     * <AUTHOR>
     * @date 2023/11/22 14:45
     */
    void acceptance(Defect defect);

    /**
     * 关闭
     *
     * @param defect
     * @return void
     * <AUTHOR>
     * @date 2023/11/22 14:46
     */
    void close(Defect defect);

    /**
     * 取消
     *
     * @param defect
     * @return void
     * <AUTHOR>
     * @date 2023/11/22 14:46
     */
    void cancel(Defect defect);

    /**
     * 延期申请
     *
     * @param defect
     * @return void
     * <AUTHOR>
     * @date 2023/11/22 14:46
     */
    void extensionRequest(Defect defect);

    /**
     * 取消审核通过
     *
     * @param defect
     * @return void
     * <AUTHOR>
     * @date 2023/11/22 14:46
     */
    void cancelAuditPass(Defect defect);

    /**
     * 延期审核通过
     *
     * @param defect
     * @return void
     * <AUTHOR>
     * @date 2023/11/22 14:46
     */
    void extensionAuditPass(Defect defect);

    /**
     * 取消审核驳回
     *
     * @param defect
     * @return void
     * <AUTHOR>
     * @date 2023/11/22 14:47
     */
    void cancelAuditReject(Defect defect);

    /**
     * 延期申请驳回
     * <AUTHOR>
     * @date 2023/11/22 14:47
     * @param defect
     * @return void
     */
    void extensionAuditReject(Defect defect);

    /**
     * 撤回延期申请
     * <AUTHOR>
     * @date 2023/11/22 14:47
     * @param defect
     * @return void
     */
    void extensionAuditWithdraw(Defect defect);
}
