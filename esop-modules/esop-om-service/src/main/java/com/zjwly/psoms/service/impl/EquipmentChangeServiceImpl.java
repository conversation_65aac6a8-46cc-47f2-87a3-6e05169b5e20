package com.zjwly.psoms.service.impl;

import cn.hutool.core.codec.Base64Encoder;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zjwly.common.satoken.utils.LoginHelper;
import com.zjwly.common.vo.IdNameVO;
import com.zjwly.common.web.utils.FastDfsUtil;
import com.zjwly.psoms.domain.Attachment;
import com.zjwly.psoms.domain.EquipmentChange;
import com.zjwly.psoms.domain.vo.EquipmentChangeVO;
import com.zjwly.psoms.domain.vo.IdNameTimeVO;
import com.zjwly.psoms.enums.EquipmentChangeTypeEnum;
import com.zjwly.psoms.mapper.EquipmentChangeMapper;
import com.zjwly.psoms.service.AttachmentService;
import com.zjwly.psoms.service.EquipmentChangeService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
public class EquipmentChangeServiceImpl extends ServiceImpl<EquipmentChangeMapper, EquipmentChange> implements EquipmentChangeService {
    private final AttachmentService attachmentService;
    private final FastDfsUtil fastDfsUtil;

    @Override
    public Integer saveReqForm(EquipmentChange equipmentChange) {
        // 拼接编号
        EquipmentChange lastOne = this.lambdaQuery().orderByDesc(EquipmentChange::getId).last("limit 1").one();
        Integer lastSerialNo = 1;
        if (lastOne != null && lastOne.getSerialNo() != null) {
            lastSerialNo = lastOne.getSerialNo();
            lastSerialNo = lastSerialNo + 1;
        }
        String serialNo = String.format("%03d", lastSerialNo);
        String reqNo = "YD-" + DateUtil.format(new Date(), DatePattern.SIMPLE_MONTH_PATTERN) + "-" + serialNo;
        equipmentChange.setSerialNo(lastSerialNo);
        equipmentChange.setReqNo(reqNo);

        Long userId = LoginHelper.getUserId();
        Date now = new Date();
        equipmentChange.setCreateByReq(userId);
        equipmentChange.setCreateTimeReq(now);
        equipmentChange.setUpdateByReq(userId);
        equipmentChange.setUpdateTimeReq(now);
        this.save(equipmentChange);
        return equipmentChange.getId();
    }

    @DSTransactional
    @Override
    public void updateReqForm(EquipmentChange equipmentChange) {
        // 如果附件发生更新，移除旧的附件
        Integer id = equipmentChange.getId();
        EquipmentChange old = this.getById(id);
        String oldBeforeAtts = old.getBeforeAtts();
        String oldAfterAtts = old.getAfterAtts();
        String oldQaReqAtts = old.getQaReqAtts();
        String beforeAtts = equipmentChange.getBeforeAtts();
        String afterAtts = equipmentChange.getAfterAtts();
        String qaReqAtts = equipmentChange.getQaReqAtts();

        // 移除无用的附件
        List<String> oldBeforeAttIds = new ArrayList<>();
        if (StrUtil.isNotBlank(oldBeforeAtts)) {
            oldBeforeAttIds.addAll(StrUtil.split(oldBeforeAtts, ","));
        }
        List<String> beforeAttIds = new ArrayList<>();
        if (StrUtil.isNotBlank(beforeAtts)) {
            beforeAttIds.addAll(StrUtil.split(beforeAtts, ","));
        }
        Collection<String> bAttIds = CollUtil.subtract(oldBeforeAttIds, beforeAttIds);

        List<String> oldAfterAttIds = new ArrayList<>();
        if (StrUtil.isNotBlank(oldAfterAtts)) {
            oldAfterAttIds.addAll(StrUtil.split(oldAfterAtts, ","));
        }
        List<String> afterAttIds = new ArrayList<>();
        if (StrUtil.isNotBlank(afterAtts)) {
            afterAttIds.addAll(StrUtil.split(afterAtts, ","));
        }
        Collection<String> aAttIds = CollUtil.subtract(oldAfterAttIds, afterAttIds);

        List<String> oldQaReqAttIds = new ArrayList<>();
        if (StrUtil.isNotBlank(oldQaReqAtts)) {
            oldQaReqAttIds.addAll(StrUtil.split(oldQaReqAtts, ","));
        }
        List<String> qaReqAttIds = new ArrayList<>();
        if (StrUtil.isNotBlank(qaReqAtts)) {
            qaReqAttIds.addAll(StrUtil.split(qaReqAtts, ","));
        }
        Collection<String> qaAttIds = CollUtil.subtract(oldQaReqAttIds, qaReqAttIds);

        Set<String> attIds = CollUtil.unionDistinct(bAttIds, aAttIds, qaAttIds);

        if (CollUtil.isNotEmpty(attIds)) {
            List<Integer> attachmentIds = new ArrayList<>();
            for (String s : attIds) {
                attachmentIds.add(Integer.valueOf(s));
            }
            // 删除附件
            List<Attachment> attachments = attachmentService.listByIds(attachmentIds);
            List<String> paths = attachments.parallelStream().map(Attachment::getPath).collect(Collectors.toList());
            attachmentService.removeByIds(attachmentIds);
            for (String path : paths) {
                fastDfsUtil.deleteFile(path);
            }
        }

        Long userId = LoginHelper.getUserId();
        Date now = new Date();
        equipmentChange.setUpdateByReq(userId);
        equipmentChange.setUpdateTimeReq(now);
        this.updateById(equipmentChange);
    }

    @DSTransactional
    @Override
    public void deleteReqForm(Integer id) {
        EquipmentChange equipmentChange = this.getById(id);
        // 删除附件
        String beforeAtts = equipmentChange.getBeforeAtts();
        List<String> beforeAttIds = new ArrayList<>();
        if (StrUtil.isNotBlank(beforeAtts)) {
            beforeAttIds.addAll(StrUtil.split(beforeAtts, ","));
        }
        String afterAtts = equipmentChange.getAfterAtts();
        List<String> afterAttIds = new ArrayList<>();
        if (StrUtil.isNotBlank(afterAtts)) {
            afterAttIds.addAll(StrUtil.split(afterAtts, ","));
        }
        String qaReqAtts = equipmentChange.getQaReqAtts();
        List<String> qaReqAttIds = new ArrayList<>();
        if (StrUtil.isNotBlank(qaReqAtts)) {
            qaReqAttIds.addAll(StrUtil.split(qaReqAtts, ","));
        }
        Set<String> attIds = CollUtil.unionDistinct(beforeAttIds, afterAttIds, qaReqAttIds);
        if (CollUtil.isNotEmpty(attIds)) {
            List<Integer> attachmentIds = new ArrayList<>();
            for (String s : attIds) {
                attachmentIds.add(Integer.valueOf(s));
            }
            // 删除附件
            List<Attachment> attachments = attachmentService.listByIds(attachmentIds);
            List<String> paths = attachments.parallelStream().map(Attachment::getPath).collect(Collectors.toList());
            attachmentService.removeByIds(attachmentIds);
            for (String path : paths) {
                fastDfsUtil.deleteFile(path);
            }
        }
        this.removeById(id);
    }

    @Override
    public EquipmentChangeVO getDetail(Integer id) {
        EquipmentChangeVO vo = this.baseMapper.getDetail(id);
        // 申请单审批成功，获取最后审批人的信息
        if (StrUtil.isNotBlank(vo.getStatusReq()) && vo.getStatusReq().equals("APPROVED")) {
            String orderIdReq = vo.getOrderIdReq();
            IdNameTimeVO idNameTimeVO = this.baseMapper.getLastTaskOperator(orderIdReq);
            vo.setGroup6Req(idNameTimeVO.getId().toString());
            vo.setGroup6ReqIdNames(ListUtil.toList(idNameTimeVO));
        }
        List<Attachment> beforeAttachments = vo.getBeforeAttachments();
        if (CollUtil.isNotEmpty(beforeAttachments)) {
            for (Attachment beforeAttachment : beforeAttachments) {
                String path = beforeAttachment.getPath();
                String base64 = getBase64(path);
                beforeAttachment.setBase64(base64);
            }
        }
        List<Attachment> afterAttachments = vo.getAfterAttachments();
        if (CollUtil.isNotEmpty(afterAttachments)) {
            for (Attachment afterAttachment : afterAttachments) {
                String path = afterAttachment.getPath();
                String base64 = getBase64(path);
                afterAttachment.setBase64(base64);
            }
        }
        return vo;
    }

    private String getBase64(String filePath) {
        String base64 = null;
        byte[] bytes = fastDfsUtil.downloadByteArray(filePath);
        if (bytes != null) {
            base64 = Base64Encoder.encode(bytes);
        }
        return base64;
    }

    @Override
    public void saveRepForm(EquipmentChange equipmentChange) {
        // 拼接编号
        EquipmentChange lastOne = this.lambdaQuery().orderByDesc(EquipmentChange::getId).last("limit 1").one();
        Integer lastSerialNo = 0;
        if (lastOne != null && lastOne.getSerialNoRep() != null) {
            lastSerialNo = lastOne.getSerialNoRep();
            lastSerialNo = lastSerialNo + 1;
        }
        String serialNo = String.format("%03d", lastSerialNo);
        String reqNo = "YDBG-" + DateUtil.format(new Date(), DatePattern.SIMPLE_MONTH_PATTERN) + "-" + serialNo;
        equipmentChange.setSerialNoRep(lastSerialNo);
        equipmentChange.setRepNo(reqNo);

        Long userId = LoginHelper.getUserId();
        Date now = new Date();

        equipmentChange.setReporter(userId);
        equipmentChange.setReportTime(now);

        equipmentChange.setCreateByRep(userId);
        equipmentChange.setCreateTimeRep(now);
        equipmentChange.setUpdateByRep(userId);
        equipmentChange.setUpdateTimeRep(now);
        this.updateById(equipmentChange);
    }

    @DSTransactional
    @Override
    public void updateRepForm(EquipmentChange equipmentChange) {
        // 如果附件发生更新，移除旧的附件
        Integer id = equipmentChange.getId();
        EquipmentChange old = this.getById(id);
        String oldLePsInvAtts = old.getLePsInvAtts();
        String oldManualAtts = old.getManualAtts();
        String oldOtherAtts = old.getOtherAtts();
        String oldStSdAtts = old.getStSdAtts();
        String oldQaRepAtts = old.getQaRepAtts();

        String lePsInvAtts = equipmentChange.getLePsInvAtts();
        String manualAtts = equipmentChange.getManualAtts();
        String otherAtts = equipmentChange.getOtherAtts();
        String stSdAtts = equipmentChange.getStSdAtts();
        String qaRepAtts = equipmentChange.getQaRepAtts();

        // 移除无用的附件
        List<String> oldLePsInvAttIds = new ArrayList<>();
        if (StrUtil.isNotBlank(oldLePsInvAtts)) {
            oldLePsInvAttIds.addAll(StrUtil.split(oldLePsInvAtts, ","));
        }
        List<String> oldManualAttIds = new ArrayList<>();
        if (StrUtil.isNotBlank(oldManualAtts)) {
            oldManualAttIds.addAll(StrUtil.split(oldManualAtts, ","));
        }
        List<String> oldOtherAttIds = new ArrayList<>();
        if (StrUtil.isNotBlank(oldOtherAtts)) {
            oldOtherAttIds.addAll(StrUtil.split(oldOtherAtts, ","));
        }
        List<String> oldStSdAttIds = new ArrayList<>();
        if (StrUtil.isNotBlank(oldStSdAtts)) {
            oldStSdAttIds.addAll(StrUtil.split(oldStSdAtts, ","));
        }
        List<String> oldQaRepAttIds = new ArrayList<>();
        if (StrUtil.isNotBlank(oldQaRepAtts)) {
            oldQaRepAttIds.addAll(StrUtil.split(oldQaRepAtts, ","));
        }

        List<String> lePsInvAttIds = new ArrayList<>();
        if (StrUtil.isNotBlank(lePsInvAtts)) {
            lePsInvAttIds.addAll(StrUtil.split(lePsInvAtts, ","));
        }
        List<String> manualAttIds = new ArrayList<>();
        if (StrUtil.isNotBlank(manualAtts)) {
            manualAttIds.addAll(StrUtil.split(manualAtts, ","));
        }
        List<String> otherAttIds = new ArrayList<>();
        if (StrUtil.isNotBlank(otherAtts)) {
            otherAttIds.addAll(StrUtil.split(otherAtts, ","));
        }
        List<String> stSdAttIds = new ArrayList<>();
        if (StrUtil.isNotBlank(stSdAtts)) {
            stSdAttIds.addAll(StrUtil.split(stSdAtts, ","));
        }
        List<String> qaRepAttIds = new ArrayList<>();
        if (StrUtil.isNotBlank(qaRepAtts)) {
            qaRepAttIds.addAll(StrUtil.split(qaRepAtts, ","));
        }

        Collection<String> lpiAttIds = CollUtil.subtract(oldLePsInvAttIds, lePsInvAttIds);
        Collection<String> mAttIds = CollUtil.subtract(oldManualAttIds, manualAttIds);
        Collection<String> oAttIds = CollUtil.subtract(oldOtherAttIds, otherAttIds);
        Collection<String> ssAttIds = CollUtil.subtract(oldStSdAttIds, stSdAttIds);
        Collection<String> qrpAttIds = CollUtil.subtract(oldQaRepAttIds, qaRepAttIds);

        Set<String> attIds = CollUtil.unionDistinct(lpiAttIds, mAttIds, oAttIds, ssAttIds, qrpAttIds);

        if (CollUtil.isNotEmpty(attIds)) {
            List<Integer> attachmentIds = new ArrayList<>();
            for (String s : attIds) {
                attachmentIds.add(Integer.valueOf(s));
            }
            // 删除附件
            List<Attachment> attachments = attachmentService.listByIds(attachmentIds);
            List<String> paths = attachments.parallelStream().map(Attachment::getPath).collect(Collectors.toList());
            attachmentService.removeByIds(attachmentIds);
            for (String path : paths) {
                fastDfsUtil.deleteFile(path);
            }
        }

        Long userId = LoginHelper.getUserId();
        Date now = new Date();

        equipmentChange.setReporter(userId);
        equipmentChange.setReportTime(now);

        equipmentChange.setUpdateByRep(userId);
        equipmentChange.setUpdateTimeRep(now);
        this.updateById(equipmentChange);
    }

    @DSTransactional
    @Override
    public void deleteRepForm(Integer id) {
        EquipmentChange equipmentChange = this.getById(id);
        String lePsInvAtts = equipmentChange.getLePsInvAtts();
        String manualAtts = equipmentChange.getManualAtts();
        String otherAtts = equipmentChange.getOtherAtts();
        String stSdAtts = equipmentChange.getStSdAtts();
        String qaRepAtts = equipmentChange.getQaRepAtts();

        List<String> lePsInvAttIds = new ArrayList<>();
        if (StrUtil.isNotBlank(lePsInvAtts)) {
            lePsInvAttIds.addAll(StrUtil.split(lePsInvAtts, ","));
        }
        List<String> manualAttIds = new ArrayList<>();
        if (StrUtil.isNotBlank(manualAtts)) {
            manualAttIds.addAll(StrUtil.split(manualAtts, ","));
        }
        List<String> otherAttIds = new ArrayList<>();
        if (StrUtil.isNotBlank(otherAtts)) {
            otherAttIds.addAll(StrUtil.split(otherAtts, ","));
        }
        List<String> stSdAttIds = new ArrayList<>();
        if (StrUtil.isNotBlank(stSdAtts)) {
            stSdAttIds.addAll(StrUtil.split(stSdAtts, ","));
        }
        List<String> qaRepAttIds = new ArrayList<>();
        if (StrUtil.isNotBlank(qaRepAtts)) {
            qaRepAttIds.addAll(StrUtil.split(qaRepAtts, ","));
        }

        Set<String> attIds = CollUtil.unionDistinct(lePsInvAttIds, manualAttIds, otherAttIds, stSdAttIds, qaRepAttIds);

        if (CollUtil.isNotEmpty(attIds)) {
            List<Integer> attachmentIds = new ArrayList<>();
            for (String s : attIds) {
                attachmentIds.add(Integer.valueOf(s));
            }
            // 删除附件
            List<Attachment> attachments = attachmentService.listByIds(attachmentIds);
            List<String> paths = attachments.parallelStream().map(Attachment::getPath).collect(Collectors.toList());
            attachmentService.removeByIds(attachmentIds);
            for (String path : paths) {
                fastDfsUtil.deleteFile(path);
            }
        }

        this.lambdaUpdate()
            .set(EquipmentChange::getRepNo, null)
            .set(EquipmentChange::getSerialNoRep, null)
            .set(EquipmentChange::getBfrRep, null)
            .set(EquipmentChange::getContent, null)
            .set(EquipmentChange::getCompletionTime, null)
            .set(EquipmentChange::getConclusion, null)
            .set(EquipmentChange::getReporter, null)
            .set(EquipmentChange::getReportTime, null)
            .set(EquipmentChange::getManual, null)
            .set(EquipmentChange::getManualAtts, null)
            .set(EquipmentChange::getLePsInv, null)
            .set(EquipmentChange::getLePsInvAtts, null)
            .set(EquipmentChange::getStSd, null)
            .set(EquipmentChange::getStSdAtts, null)
            .set(EquipmentChange::getOther, null)
            .set(EquipmentChange::getOtherAtts, null)
            .set(EquipmentChange::getQaRep, null)
            .set(EquipmentChange::getGroup1Rep, null)
            .set(EquipmentChange::getGroup2Rep, null)
            .set(EquipmentChange::getGroup3Rep, null)
            .set(EquipmentChange::getGroup4Rep, null)
            .set(EquipmentChange::getGroup5Rep, null)
            .set(EquipmentChange::getCreateByRep, null)
            .set(EquipmentChange::getCreateTimeRep, null)
            .set(EquipmentChange::getUpdateByRep, null)
            .set(EquipmentChange::getUpdateTimeRep, null)
            .eq(EquipmentChange::getId, id)
            .update();
    }

    @Override
    public IPage<EquipmentChangeVO> listInfo(String reqNo, String equipmentName, String applicantUnit, String applicant, Integer current, Integer size) {
        IPage<EquipmentChangeVO> page = new Page<>(current, size);
        return this.baseMapper.listInfo(page, reqNo, equipmentName, applicantUnit, applicant);
    }

    @Override
    public Map<String, String> getCountersignGroup(String orderId) {
        Map<String, String> result = new HashMap<>();
        Map<String, Object> extOperationMap = this.baseMapper.getExtOperationByOrderId(orderId);
        Integer id = (Integer) extOperationMap.get("businessId");
        EquipmentChange vo = this.getById(id);
        if (vo != null) {
            String businessType = (String) extOperationMap.get("businessType");
            if (EquipmentChangeTypeEnum.EQUIPMENT_CHANGE_REQUEST_FORM.name().equals(businessType)) {
                // 申请单
                List<IdNameVO> group1ReqIdNames = this.baseMapper.getGroup(vo.getGroup1Req());
                if (CollUtil.isNotEmpty(group1ReqIdNames)) {
                    for (IdNameVO idName : group1ReqIdNames) {
                        result.put(idName.getName(), "主工序班组");
                    }
                }
                List<IdNameVO> group2ReqIdNames = this.baseMapper.getGroup(vo.getGroup2Req());
                if (CollUtil.isNotEmpty(group2ReqIdNames)) {
                    for (IdNameVO idName : group2ReqIdNames) {
                        result.put(idName.getName(), "相关班组");
                    }
                }
                List<IdNameVO> group3ReqIdNames =this.baseMapper.getGroup(vo.getGroup3Req());
                if (CollUtil.isNotEmpty(group3ReqIdNames)) {
                    for (IdNameVO idName : group3ReqIdNames) {
                        result.put(idName.getName(), "运行班组");
                    }
                }
                /*List<IdNameTimeVO> group4ReqIdNames = vo.getGroup4ReqIdNames();
                if (CollUtil.isNotEmpty(group4ReqIdNames)) {
                    for (IdNameTimeVO idName : group4ReqIdNames) {
                        result.put(idName.getName(), "副站长");
                    }
                }
                List<IdNameTimeVO> group5ReqIdNames = vo.getGroup5ReqIdNames();
                if (CollUtil.isNotEmpty(group5ReqIdNames)) {
                    for (IdNameTimeVO idName : group5ReqIdNames) {
                        result.put(idName.getName(), "副站长");
                    }
                }
                List<IdNameTimeVO> group6ReqIdNames = vo.getGroup6ReqIdNames();
                if (CollUtil.isNotEmpty(group6ReqIdNames)) {
                    for (IdNameTimeVO idName : group6ReqIdNames) {
                        result.put(idName.getName(), "站长");
                    }
                }*/
            } else if (EquipmentChangeTypeEnum.PRODUCTION_EQUIPMENT_CHANGE_REPORT_FORM.name().equals(businessType)) {
                // 报告单
                List<IdNameVO> group1RepIdNames = this.baseMapper.getGroup(vo.getGroup1Rep());
                if (CollUtil.isNotEmpty(group1RepIdNames)) {
                    for (IdNameVO idName : group1RepIdNames) {
                        result.put(idName.getName(), "主工序班组");
                    }
                }
                List<IdNameVO> group2RepIdNames =  this.baseMapper.getGroup(vo.getGroup2Rep());
                if (CollUtil.isNotEmpty(group2RepIdNames)) {
                    for (IdNameVO idName : group2RepIdNames) {
                        result.put(idName.getName(), "相关班组");
                    }
                }
                List<IdNameVO> group3RepIdNames = this.baseMapper.getGroup(vo.getGroup3Rep());
                if (CollUtil.isNotEmpty(group3RepIdNames)) {
                    for (IdNameVO idName : group3RepIdNames) {
                        result.put(idName.getName(), "运行班组");
                    }
                }
            }
        }
        return result;
    }

    @Override
    public Map<Integer,Map<String, Object>> selectLatestStatusByBusinessIds(List<Integer> businessIds) {

        return this.baseMapper.selectLatestStatusByBusinessIds(businessIds);
    }
}
