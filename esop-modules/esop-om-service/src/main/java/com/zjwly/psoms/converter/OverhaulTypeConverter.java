package com.zjwly.psoms.converter;

import com.alibaba.excel.converters.integer.IntegerStringConverter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

/**
 * 检修台账类别转换器
 *
 * <AUTHOR>
 * @date 2023/11/08 15:28
 */
public class OverhaulTypeConverter extends IntegerStringConverter {
    @Override
    public WriteCellData<?> convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        String v;
        switch (value) {
            case 1:
                v = "A类";
                break;
            case 2:
                v = "B类";
                break;
            case 3:
                v = "C类";
                break;
            default:
                v = "";
        }
        return new WriteCellData<>(v);
    }
}
