package com.zjwly.psoms.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.zjwly.psoms.domain.Equipment;
import com.zjwly.psoms.enums.EquipmentTypeEnum;
import com.zjwly.psoms.service.EquipmentService;

import java.util.List;

/**
 * 设备数据监听器
 *
 * <AUTHOR>
 * @date 2023/10/26 20:53
 */
public class EquipmentDataListener implements ReadListener<Equipment> {
    /**
     * 每隔5条存储数据库，实际使用中可以100条，然后清理list ，方便内存回收
     */
    private static final int BATCH_COUNT = 100;
    /**
     * 缓存的数据
     */
    private List<Equipment> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

    private EquipmentService equipmentService;

    public EquipmentDataListener(EquipmentService equipmentService) {
        this.equipmentService = equipmentService;
    }

    /**
     * 这个每一条数据解析都会来调用
     *
     * @param data
     * @param context
     * @return void
     * <AUTHOR>
     * @date 2023/10/26 20:59
     */
    @Override
    public void invoke(Equipment data, AnalysisContext context) {
        data.setProcessType(EquipmentTypeEnum.OMS_CREATE.name());
        cachedDataList.add(data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        equipmentService.saveInfoBatch(cachedDataList);
    }
}
