package com.zjwly.psoms.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zjwly.common.util.BaseChangeLog;
import com.zjwly.psoms.enums.DefectOpEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 缺陷修改记录
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "om_defect_change_log")
public class DefectChangeLog extends BaseChangeLog {

    /**
     * 缺陷ID
     */
    @TableField(value = "defect_id")
    private Integer defectId;

    /**
     * 操作类型：操作类型：
     * SAVE(0, "新增"),
     * UPDATE(1, "修改"),
     * SUBMIT(2, "提交"),
     * CONFIRM(3, "确认"),
     * DISTRIBUTE(4, "分发"),
     * COMPLETE(5, "完成"),
     * ACCEPTANCE(6, "验收"),
     * CLOSE(7, "关闭"),
     * EXTENSION_REQUEST(8, "延期申请"),
     * WITHDRAW(9, "撤回"),
     * EXTENSION_PASS(10, "延期审核通过"),
     * EXTENSION_REJECT(11, "延期审核驳回"),
     * CANCEL(12, "取消"),
     * CANCEL_PASSW(13, "取消审核通过"),
     * CANCEL_REJECT(14, "取消审核驳回"),
     * DELETE(15, "删除");
     */
    @TableField(value = "op_type")
    private DefectOpEnum opType;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 延期原因
     */
    @TableField("extension_reason")
    private String extensionReason;

    /**
     * 延期时间
     */
    @TableField("extension_time")
    private Date extensionTime;

    /**
     * 工作安排
     */
    @TableField("work_content")
    private String workContent;
}
