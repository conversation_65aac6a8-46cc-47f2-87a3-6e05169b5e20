package com.zjwly.psoms.domain.vo;

import lombok.Data;

/**
 * 首页VO
 *
 * <AUTHOR>
 * @date 2023/11/20 14:33
 */
@Data
public class HomePageVO {
    /**
     * 待接班总数
     */
    private Integer allATOCount;
    /**
     * 当天新增数量
     */
    private Integer todayATOCount;
    /**
     * 缺陷总数（缺陷状态除【草稿】、【缺陷关闭】的其他所有状态合计）
     */
    Integer allDSCount;
    /**
     * 当天新增缺陷（缺陷状态除【草稿】、【缺陷关闭】的其他所有状态合计）
     */
    Integer todayDSCount;
    /**
     * 超期的合计
     */
    Integer overdueCount;
    /**
     * 设备待审批总数
     */
    Integer allEACount;
    /**
     * 当天设备待审批新增数量
     */
    Integer todayEACount;
    /**
     * 设备异动待审批总数
     */
    Integer allECACount;
    /**
     * 当天设备异动待审批新增数量
     */
    Integer todayECACount;
}
