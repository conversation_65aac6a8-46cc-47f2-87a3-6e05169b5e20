package com.zjwly.psoms.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zjwly.psoms.domain.EquipmentChange;
import com.zjwly.psoms.domain.vo.EquipmentChangeVO;

import java.util.List;
import java.util.Map;

public interface EquipmentChangeService extends IService<EquipmentChange> {


    /**
     * 异动申请
     *
     * @param equipmentChange
     * @return void
     * <AUTHOR>
     * @date 2023/11/10 17:26
     */
    Integer saveReqForm(EquipmentChange equipmentChange);

    /**
     * 填报申请
     *
     * @param equipmentChange
     * @return void
     * <AUTHOR>
     * @date 2023/11/10 17:31
     */
    void updateReqForm(EquipmentChange equipmentChange);

    /**
     * 删除申请单
     *
     * @param id 设备异动ID
     * @return void
     * <AUTHOR>
     * @date 2023/11/13 16:21
     */
    void deleteReqForm(Integer id);

    /**
     * 获取详情
     *
     * @param id 设备异动ID
     * @return com.zjwly.psoms.domain.vo.EquipmentChangeVO
     * <AUTHOR>
     * @date 2023/11/13 17:47
     */
    EquipmentChangeVO getDetail(Integer id);

    /**
     * 生成报告单
     *
     * @param equipmentChange
     * @return void
     * <AUTHOR>
     * @date 2023/11/14 10:08
     */
    void saveRepForm(EquipmentChange equipmentChange);

    /**
     * 编辑报告单
     *
     * @param equipmentChange
     * @return void
     * <AUTHOR>
     * @date 2023/11/14 10:13
     */
    void updateRepForm(EquipmentChange equipmentChange);

    /**
     * 删除报告单
     *
     * @param id
     * @return void
     * <AUTHOR>
     * @date 2023/11/14 10:44
     */
    void deleteRepForm(Integer id);

    /**
     * 分页查询列表
     *
     * @param reqNo         编号
     * @param equipmentName 设备名称
     * @param applicantUnit 申请单位
     * @param applicant     申请人
     * @param current       当前页
     * @param size          每页条数
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.zjwly.psoms.domain.vo.EquipmentChangeVO>
     * <AUTHOR>
     * @date 2023/11/14 16:42
     */
    IPage<EquipmentChangeVO> listInfo(String reqNo, String equipmentName, String applicantUnit, String applicant, Integer current, Integer size);

    /**
     * 根据审批单号获取班组
     *
     * @param orderId 审批单号
     * @return java.util.Map<java.lang.String, java.lang.String>
     * <AUTHOR>
     * @date 2024/01/15 17:06
     */
    Map<String, String> getCountersignGroup(String orderId);



    Map<Integer,Map<String, Object>> selectLatestStatusByBusinessIds( List<Integer> businessIds);
}
