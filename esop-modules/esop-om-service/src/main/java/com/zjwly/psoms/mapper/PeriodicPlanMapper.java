package com.zjwly.psoms.mapper;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zjwly.psoms.domain.PeriodicPlan;
import com.zjwly.psoms.domain.vo.PeriodicPlanVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PeriodicPlanMapper extends BaseMapper<PeriodicPlan> {
    IPage<PeriodicPlanVO> listInfo(IPage<PeriodicPlanVO> page, @Param("startTime") DateTime startTime,
                                   @Param("endTime") DateTime endTime,
                                   @Param("cycleType") Integer cycleType,
                                   @Param("status") Integer status, @Param("taskContent") String taskContent);

    IPage<PeriodicPlanVO> listEditRecord(IPage<PeriodicPlanVO> page, @Param("lotNo") String lotNo);

    List<PeriodicPlan> listSchedulerData(@Param("startTime") String startTime,
                                           @Param("endTime") String endTime,
                                           @Param("status") Integer status);
}