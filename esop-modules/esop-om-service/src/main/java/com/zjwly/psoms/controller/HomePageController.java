package com.zjwly.psoms.controller;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.zjwly.common.base.Result;
import com.zjwly.common.satoken.utils.LoginHelper;
import com.zjwly.psoms.domain.Defect;
import com.zjwly.psoms.domain.DutyOperationLog;
import com.zjwly.psoms.domain.vo.HomePageVO;
import com.zjwly.psoms.enums.DefectStateEnum;
import com.zjwly.psoms.enums.DutyOpStatusEnum;
import com.zjwly.psoms.mapper.EquipmentChangeMapper;
import com.zjwly.psoms.mapper.EquipmentMapper;
import com.zjwly.psoms.service.DefectService;
import com.zjwly.psoms.service.DutyOperationLogService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 首页
 *
 * <AUTHOR>
 * @date 2023/11/20 14:16
 */
@RequiredArgsConstructor
@RequestMapping("/ds/om/homePage")
@RestController
public class HomePageController {
    private final DutyOperationLogService dutyOperationLogService;
    private final DefectService defectService;
    private final EquipmentMapper equipmentMapper;
    private final EquipmentChangeMapper equipmentChangeMapper;

    /**
     * 首页
     *
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/11/20 18:06
     * @response {@link Result<HomePageVO>}
     */
    @GetMapping
    public Result homePage() {
        HomePageVO homePageVO = new HomePageVO();
        String todayStr = DateUtil.today();
        DateTime today = DateUtil.parseDate(todayStr);
        DateTime tomorrow = DateUtil.offsetDay(today, 1);
        // 待接班总数
        Long allATOCount = dutyOperationLogService.lambdaQuery()
                                                     .eq(DutyOperationLog::getOpStatus, DutyOpStatusEnum.AWAITING_TAKEOVER.getCode())
                                                     .count();
        homePageVO.setAllATOCount(Math.toIntExact(allATOCount));
        // 当天新增数量
        Long todayATOCount = dutyOperationLogService.lambdaQuery()
                                                       .eq(DutyOperationLog::getOpStatus, DutyOpStatusEnum.AWAITING_TAKEOVER.getCode())
                                                       .ge(DutyOperationLog::getCreateTime, today)
                                                       .lt(DutyOperationLog::getCreateTime, tomorrow)
                                                       .count();
        homePageVO.setTodayATOCount(Math.toIntExact(todayATOCount));

        // 缺陷状态除【草稿】、【缺陷关闭】、【工单取消】的其他所有状态合计
        Long allDSCount = defectService.lambdaQuery().ne(Defect::getProcessingStatus, DefectStateEnum.DRAFT)
                                          .ne(Defect::getProcessingStatus, DefectStateEnum.DEFECT_CLOSED)
                                          .ne(Defect::getProcessingStatus, DefectStateEnum.WORK_ORDER_CANCELED)
                                          .count();
        homePageVO.setAllDSCount(Math.toIntExact(allDSCount));
        // 当天数量
        Long todayDSCount = defectService.lambdaQuery().ne(Defect::getProcessingStatus, DefectStateEnum.DRAFT)
                                            .ne(Defect::getProcessingStatus, DefectStateEnum.DEFECT_CLOSED)
                                            .ne(Defect::getProcessingStatus, DefectStateEnum.WORK_ORDER_CANCELED)
                                            .ge(Defect::getCreateTime, today)
                                            .lt(Defect::getCreateTime, tomorrow)
                                            .count();
        homePageVO.setTodayDSCount(Math.toIntExact(todayDSCount));
        // 超期的总数
        Long overdueCount = defectService.lambdaQuery().eq(Defect::getIsOverdue, 1).count();
        homePageVO.setOverdueCount(Math.toIntExact(overdueCount));

        Long userId = LoginHelper.getUserId();
        Integer allEACount = equipmentMapper.getApprovingCount(userId, null, null);
        homePageVO.setAllEACount(allEACount);
        Integer todayEACount = equipmentMapper.getApprovingCount(userId, today, tomorrow);
        homePageVO.setTodayEACount(todayEACount);

        Integer allECACount = equipmentChangeMapper.getApprovingCount(userId, null, null);
        homePageVO.setAllECACount(allECACount);
        Integer todayECACount = equipmentChangeMapper.getApprovingCount(userId, today, tomorrow);
        homePageVO.setTodayECACount(todayECACount);
        return Result.ok(homePageVO);
    }

}
