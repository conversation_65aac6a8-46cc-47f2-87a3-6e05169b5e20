package com.zjwly.psoms.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 缺陷管理状态枚举
 *
 * <AUTHOR>
 * @date 2023/10/31 19:25
 */
public enum DefectStateEnum {
    /**
     * 草稿
     */
    DRAFT("DRAFT", "草稿", 1),
    /**
     * 缺陷录入
     */
    DEFECT_ENTRY("DEFECT_ENTRY", "缺陷录入", 2),
    /**
     * 缺陷下达
     */
    DEFECT_ISSUED("DEFECT_ISSUED", "缺陷下达", 3),
    /**
     * 工作安排
     */
    WORK_ASSIGNMENT("WORK_ASSIGNMENT", "工作安排", 4),
    /**
     * 工作完成
     */
    WORK_COMPLETED("WORK_COMPLETED", "工作完成", 5),
    /**
     * 验收合格
     */
    ACCEPTANCE("ACCEPTANCE", "验收合格", 6),
    /**
     * 缺陷关闭
     */
    DEFECT_CLOSED("DEFECT_CLOSED", "缺陷关闭", 7),
    /**
     * 工单取消申请
     */
    WORK_ORDER_CANCELED_REQUEST("WORK_ORDER_CANCELED_REQUEST", "工单取消申请", 8),
    /**
     * 延期申请
     */
    EXTENSION_REQUEST("EXTENSION_REQUEST", "延期申请", 9),
    /**
     * 工单取消
     */
    WORK_ORDER_CANCELED("WORK_ORDER_CANCELED", "工单取消", 10),
    /**
     * 延期申请通过
     */
    EXTENSION_APPROVED("EXTENSION_APPROVED", "延期申请通过", 0);

    @Getter
    private String value;
    @Getter
    private String displayName;
    @EnumValue
    @Getter
    private Integer code;

    DefectStateEnum(String value, String displayName, Integer code) {
        this.value = value;
        this.displayName = displayName;
        this.code = code;
    }

    private static final Map<Integer, DefectStateEnum> CACHE;
    private static final Map<String, DefectStateEnum> CACHE2;

    static {
        CACHE = Arrays.stream(DefectStateEnum.values())
                      .collect(Collectors.toMap(DefectStateEnum::getCode, Function.identity()));
        CACHE2 = Arrays.stream(DefectStateEnum.values())
                       .collect(Collectors.toMap(DefectStateEnum::getValue, Function.identity()));
    }


    public static DefectStateEnum getByCode(Integer code) {
        return CACHE.get(code);
    }

    public static DefectStateEnum getByValue(String value) {
        return CACHE2.get(value);
    }

    @Override
    public String toString() {
        return this.value;
    }
}
