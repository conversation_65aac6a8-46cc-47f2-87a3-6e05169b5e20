package com.zjwly.psoms.controller;

import cn.hutool.core.util.IdUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zjwly.common.base.Result;
import com.zjwly.common.easyexcel.CustomCellWriteWeightConfig;
import com.zjwly.psoms.domain.Attachment;
import com.zjwly.psoms.domain.Equipment;
import com.zjwly.psoms.domain.vo.EquipmentVO;
import com.zjwly.psoms.domain.vo.EquipmentVersionVO;
import com.zjwly.psoms.enums.EquipmentTypeEnum;
import com.zjwly.psoms.listener.EquipmentDataListener;
import com.zjwly.psoms.mapper.EquipmentMapper;
import com.zjwly.psoms.service.EquipmentService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

/**
 * 设备台账Controller
 *
 * <AUTHOR>
 * @date 2023/10/25 10:32
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/ds/om/equipment")
public class EquipmentController {
    private final EquipmentService equipmentService;
    private final EquipmentMapper equipmentMapper;

    /**
     * 保存设备
     *
     * @param equipment
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/10/25 10:42
     */
    @PostMapping("/save")
    public Result save(@RequestBody Equipment equipment) {
        equipmentService.checkName(equipment.getName());
        equipment.setProcessType(EquipmentTypeEnum.OMS_CREATE.name());
        equipment.setLotNo(IdUtil.simpleUUID());
        equipmentService.save(equipment);
        return Result.ok();
    }

    /**
     * 编辑
     *
     * @param equipment
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/10/25 14:15
     */
    @PutMapping("/update")
    public Result update(@RequestBody Equipment equipment) {
        equipmentService.updateById(equipment);
        return Result.ok();
    }

    /**
     * 变更确认
     *
     * @param equipment
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/11/02 13:54
     */
    @PutMapping("/updateProcess")
    public Result updateProcess(@RequestBody Equipment equipment) {
        equipmentService.updateInfo(equipment);
        return Result.ok();
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/10/25 14:19
     */
    @DeleteMapping("/delete")
    public Result delete(@RequestBody List<Integer> ids) {
        equipmentService.delete(ids);
        return Result.ok();
    }

    /**
     * 操作区列表
     *
     * @param name           设备名称
     * @param no             设备编号
     * @param type           类别 DZ_OMS_CREATE,DZ_OMS_UPDATE,DZ_OMS_PHASEOUT
     * @param approvalStatus 审批状态 APPROVING("审批中"),REJECTED("已驳回"),CANCELED("已撤销"),
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/10/25 14:44
     */
    @GetMapping("/listOpArea")
    public Result listOpArea(String name, String no, String type, String approvalStatus,
                             @RequestParam(value = "current", required = false, defaultValue = "-1") Long current,
                             @RequestParam(value = "size", required = false, defaultValue = "-1") Long size) {
        // 审批流状态
        //    UNCOMMITTED("未提交"),
        //    APPROVING("审批中"),
        //    APPROVED("审批完成"),
        //    REJECTED("已驳回"),
        //    CANCELED("已销"),
        //    OBSOLETE("已废除")

        // 类型：DZ_OMS_CREATE,DZ_OMS_UPDATE,DZ_OMS_PHASEOUT
        IPage<EquipmentVO> vos = equipmentService.listOpArea(name, no, type, approvalStatus, current, size);
        return Result.ok(vos);
    }

    /**
     * 设备台账
     *
     * @param name    设备名称
     * @param no      设备编号
     * @param type    类别
     * @param current
     * @param size
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/10/25 22:07
     */
    @GetMapping("/listInfo")
    public Result listInfo(String name, String no, String type,
                           @RequestParam(value = "current", required = false, defaultValue = "-1") Long current,
                           @RequestParam(value = "size", required = false, defaultValue = "-1") Long size) {
        IPage<EquipmentVO> vos = equipmentService.listInfo(name, no, type, current, size);
        return Result.ok(vos);
    }

    /**
     * 版本列表
     *
     * @param lotNo 批号
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/10/26 09:40
     */
    @GetMapping("/listVersion")
    public Result listVersion(@RequestParam("lotNo") String lotNo) {
        List<EquipmentVersionVO> vos = equipmentService.listVersion(lotNo);
        return Result.ok(vos);
    }

    @GetMapping("/export")
    public void export(String name, String no, String type,
                       @RequestParam(value = "current", required = false, defaultValue = "-1") Long current,
                       @RequestParam(value = "size", required = false, defaultValue = "-1") Long size,
                       HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("设备台账", "UTF-8")
                                    .replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        IPage<EquipmentVO> vos = equipmentService.listInfo(name, no, type, current, size);
        List<EquipmentVO> records = vos.getRecords();
        EasyExcel.write(response.getOutputStream(), EquipmentVO.class)
                 .registerWriteHandler(new CustomCellWriteWeightConfig())
                 .sheet()
                 .doWrite(records);
    }

    /**
     * 详情
     *
     * @param id
     * @param status 审批状态 APPROVING("审批中"),REJECTED("已驳回"),CANCELED("已撤销"),APPROVED("审批通过")
     * @param type   类别 DZ_OMS_CREATE,DZ_OMS_UPDATE,DZ_OMS_PHASEOUT
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/10/26 16:45
     */
    @GetMapping("/getDetail")
    public Result getDetail(@RequestParam("id") Integer id, String status, String type) {
        EquipmentVO vo = equipmentService.getDetail(id, status, type);
        return Result.ok(vo);
    }

    /**
     * 批量上传
     *
     * @param files
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/10/26 19:47
     */
    @PostMapping("/upload")
    public Result upload(@RequestParam("files") List<MultipartFile> files) throws IOException {
        List<Attachment> attachments = equipmentService.upload(files);
        return Result.ok(attachments);
    }

    /**
     * 新增导入
     *
     * @param file
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/10/26 21:07
     */
    @PostMapping("/addImport")
    public Result addImport(MultipartFile file) throws IOException {
        EasyExcel.read(file.getInputStream(), Equipment.class, new EquipmentDataListener(equipmentService))
                 .sheet()
                 .doRead();
        return Result.ok();
    }

    /**
     * 获取非淘汰的审批通过列表
     *
     * @param name
     * @param no
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/10/27 10:52
     */
    @GetMapping("/listApprovedInfo")
    public Result listApprovedInfo(String name, String no) {
        List<EquipmentVO> vos = equipmentMapper.listApprovedInfo(name, no);
        return Result.ok(vos);
    }

    /**
     * 获取当前设备审批通过的业务ID
     *
     * @param lotNo 批号
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/11/03 14:43
     */
    @GetMapping("/listApprovedIds")
    public Result listApprovedIds(@RequestParam("lotNo") String lotNo) {
        List<Integer> ids = equipmentService.listApprovedIds(lotNo);
        return Result.ok(ids);
    }
}
