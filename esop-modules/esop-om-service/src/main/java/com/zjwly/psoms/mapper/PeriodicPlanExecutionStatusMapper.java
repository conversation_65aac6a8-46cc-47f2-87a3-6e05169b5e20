package com.zjwly.psoms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zjwly.psoms.domain.PeriodicPlanExecutionStatus;
import com.zjwly.psoms.domain.vo.PeriodicPlanExecutionStatusVO;
import org.apache.ibatis.annotations.Param;

public interface PeriodicPlanExecutionStatusMapper extends BaseMapper<PeriodicPlanExecutionStatus> {
    IPage<PeriodicPlanExecutionStatusVO> listInfo(IPage<PeriodicPlanExecutionStatusVO> page,
                                                  @Param("cycleType") Integer cycleType,
                                                  @Param("startDate") String startDate, @Param("endDate") String endDate,
                                                  @Param("shiftType") Integer shiftType, @Param("status") Integer status,
                                                  @Param("executorId") Long executorId, @Param("taskContent") String taskContent);

    Integer countByPpIdAndDate(@Param("ppId") Integer ppId, @Param("startTime") String startTime,
                               @Param("endTime") String endTime);
}