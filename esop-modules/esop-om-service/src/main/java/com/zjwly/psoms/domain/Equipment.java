package com.zjwly.psoms.domain;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zjwly.common.mybatis.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 设备信息
 * EQUIPMENT_CHANGE_REQUEST_FORM 设备异动申请单审批
 * PRODUCTION_EQUIPMENT_CHANGE_REPORT_FORM 生产设备异动报告单审批
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "om_equipment")
public class Equipment extends BaseEntity {
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 批号
     */
    private String lotNo;

    /**
     * 名称
     */
    @ExcelProperty("设备名称")
    @TableField(value = "`name`")
    private String name;

    /**
     * 编号
     */
    @ExcelProperty("设备编号")
    @TableField(value = "`no`")
    private String no;

    /**
     * 规格型号
     */
    @ExcelProperty("规格型号")
    @TableField(value = "spec_model")
    private String specModel;

    /**
     * 主要参数
     */
    @ExcelProperty("主要参数")
    @TableField(value = "param")
    private String param;

    /**
     * 制造厂家
     */
    @ExcelProperty("制造厂家")
    @TableField(value = "manufacturer")
    private String manufacturer;

    /**
     * 进站日期
     */
    @ExcelProperty("进站日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @TableField(value = "arrival_date")
    private Date arrivalDate;

    /**
     * 使用日期
     */
    @ExcelProperty("使用日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @TableField(value = "usage_date")
    private Date usageDate;

    /**
     * 使用地点
     */
    @ExcelProperty("使用地点")
    @TableField(value = "`location`")
    private String location;

    /**
     * 附件ID逗号分隔
     */
    private String attachmentIds;

    /**
     * 流程类型 创建：DZ_OMS_CREATE，更新：DZ_OMS_UPDATE
     */
    private String processType;

}
