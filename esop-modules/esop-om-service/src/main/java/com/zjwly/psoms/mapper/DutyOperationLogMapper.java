package com.zjwly.psoms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zjwly.psoms.domain.DutyOperationLog;
import com.zjwly.psoms.domain.vo.DutyCalendarVO;
import com.zjwly.psoms.domain.vo.DutyOpVersionVO;
import com.zjwly.psoms.domain.vo.DutyOperationLogVO;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface DutyOperationLogMapper extends BaseMapper<DutyOperationLog> {
    IPage<DutyOperationLogVO> listPage(IPage<DutyOperationLogVO> page,
                                       @Param("startTime") String startTime, @Param("endTime") String endTime,
                                       @Param("type") Integer type, @Param("handoverDuty") Integer handoverDuty,
                                       @Param("handoverName") String handoverName, @Param("takeoverDuty") Integer takeoverDuty,
                                       @Param("takeoverName") String takeoverName, @Param("status") Integer status);

    List<DutyOperationLogVO> listByLotNo(@Param("lotNo") String lotNo);

    List<DutyOpVersionVO> listVersion(@Param("lotNo") String lotNo);

    DutyOperationLogVO detail(@Param("id") Integer id);

    @MapKey("date")
    Map<String, DutyCalendarVO> listCalendar(@Param("startTime") Date startTime, @Param("endTime") Date endTime);
}