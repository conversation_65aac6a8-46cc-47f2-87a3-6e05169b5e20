package com.zjwly.psoms.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 设备异动申请单
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "om_equipment_change")
public class EquipmentChange {
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 申请单编号
     */
    @TableField(value = "req_no")
    private String reqNo;

    /**
     * 序号
     */
    @TableField(value = "serial_no")
    private Integer serialNo;

    /**
     * 设备ID
     */
    @TableField(value = "equipment_id")
    private Integer equipmentId;

    /**
     * 设备批号
     */
    @TableField(value = "equipment_lot_no")
    private String equipmentLotNo;

    /**
     * 设备名称
     */
    @TableField(value = "equipment_name")
    private String equipmentName;

    /**
     * 设备编号
     */
    @TableField(value = "equipment_no")
    private String equipmentNo;

    /**
     * 异动原因
     */
    @TableField(value = "change_reason")
    private String changeReason;

    /**
     * 申请单位
     */
    @TableField(value = "applicant_unit")
    private String applicantUnit;

    /**
     * 申请人
     */
    @TableField(value = "applicant")
    private String applicant;

    /**
     * 申请时间
     */
    @TableField(value = "applicant_time")
    private Date applicantTime;

    /**
     * 专业
     */
    private String specialty;

    /**
     * 异动内容：异动前
     */
    @TableField(value = "`before`")
    private String before;

    /**
     * 异动内容：异动前附件ID逗号分隔
     */
    @TableField(value = "before_atts")
    private String beforeAtts;

    /**
     * 异动内容：异动后
     */
    @TableField(value = "`after`")
    private String after;

    /**
     * 异动内容：异动后附件ID逗号分隔
     */
    @TableField(value = "after_atts")
    private String afterAtts;

    /**
     * 可能出现的问题及注意事项
     */
    @TableField(value = "qa_req")
    private String qaReq;

    /**
     * 可能出现的问题及注意事项附件ID逗号分隔
     */
    @TableField(value = "qa_req_atts")
    private String qaReqAtts;

    /**
     * 申请单会签第1组人逗号分隔
     */
    @TableField(value = "group1_req")
    private String group1Req;

    /**
     * 申请单会签第2组人逗号分隔
     */
    @TableField(value = "group2_req")
    private String group2Req;

    /**
     * 申请单会签第3组人逗号分隔
     */
    @TableField(value = "group3_req")
    private String group3Req;

    /**
     * 申请单会签第4组人逗号分隔
     */
    @TableField(value = "group4_req")
    private String group4Req;

    /**
     * 申请单会签第5组人逗号分隔
     */
    @TableField(value = "group5_req")
    private String group5Req;

    /**
     * 申请单第6组
     */
    @TableField(value = "group6_req")
    private String group6Req;

    /**
     * 申请单创建人
     */
    @TableField(value = "create_by_req")
    private Long createByReq;

    /**
     * 申请单创建时间
     */
    @TableField(value = "create_time_req")
    private Date createTimeReq;

    /**
     * 申请单更新人
     */
    @TableField(value = "update_by_req")
    private Long updateByReq;

    /**
     * 申请单更新时间
     */
    @TableField(value = "update_time_req")
    private Date updateTimeReq;

    // 以下为报告单数据

    /**
     * 报告单编号
     */
    @TableField(value = "rep_no")
    private String repNo;

    /**
     * 序号
     */
    @TableField(value = "serial_no_rep")
    private Integer serialNoRep;

    /**
     * 设备异动情况：异动前
     */
    @TableField(value = "bfr_rep")
    private String bfrRep;

    /**
     * 设备异动情况：异动内容
     */
    @TableField(value = "content")
    private String content;

    /**
     * 异动完成时间
     */
    @TableField(value = "completion_time")
    private Date completionTime;

    /**
     * 结论
     */
    @TableField(value = "conclusion")
    private String conclusion;

    /**
     * 填报人
     */
    @TableField(value = "reporter")
    private Long reporter;

    /**
     * 填报时间
     */
    @TableField(value = "report_time")
    private Date reportTime;

    /**
     * 异动设备说明书等相关资料是否已上传附件
     */
    @TableField(value = "manual")
    private String manual;

    /**
     * 异动设备说明书等相关资料附件ID逗号分隔
     */
    @TableField(value = "manual_atts")
    private String manualAtts;

    /**
     * 是否需要修编逻辑说明、保护定值/清册等资料
     */
    @TableField(value = "le_ps_inv")
    private String lePsInv;

    /**
     * 逻辑说明、保护定值/清册等资料附件ID逗号分隔
     */
    @TableField(value = "le_ps_inv_atts")
    private String lePsInvAtts;

    /**
     * 是否需要修编系统图、结构图等有关图纸资料
     */
    @TableField(value = "st_sd")
    private String stSd;

    /**
     * 系统图、结构图等有关图纸资料附件ID逗号分隔
     */
    @TableField(value = "st_sd_atts")
    private String stSdAtts;

    /**
     * 其他异动资料填报（如有）
     */
    @TableField(value = "other")
    private String other;

    /**
     * 其他异动资料附件ID逗号分隔
     */
    @TableField(value = "other_atts")
    private String otherAtts;

    /**
     * 可能出现的问题及注意事项
     */
    @TableField(value = "qa_rep")
    private String qaRep;

    /**
     * 可能出现的问题及注意事项
     */
    @TableField(value = "qa_rep_atts")
    private String qaRepAtts;

    /**
     * 报告单会签第1组人逗号分隔
     */
    @TableField(value = "group1_rep")
    private String group1Rep;

    /**
     * 报告单会签第2组人逗号分隔
     */
    @TableField(value = "group2_rep")
    private String group2Rep;

    /**
     * 报告单会签第3组人逗号分隔
     */
    @TableField(value = "group3_rep")
    private String group3Rep;

    /**
     * 报告单会签第4组人逗号分隔
     */
    @TableField(value = "group4_rep")
    private String group4Rep;

    /**
     * 报告单会签第5组人逗号分隔
     */
    @TableField(value = "group5_rep")
    private String group5Rep;

    /**
     * 报告单创建人
     */
    @TableField(value = "create_by_rep")
    private Long createByRep;

    /**
     * 报告单创建时间
     */
    @TableField(value = "create_time_rep")
    private Date createTimeRep;

    /**
     * 报告单更新人
     */
    @TableField(value = "update_by_rep")
    private Long updateByRep;

    /**
     * 报告单更新时间
     */
    @TableField(value = "update_time_rep")
    private Date updateTimeRep;
}
