package com.zjwly.psoms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zjwly.common.exception.ServiceException;
import com.zjwly.common.web.utils.FastDfsUtil;
import com.zjwly.psoms.domain.Attachment;
import com.zjwly.psoms.domain.Equipment;
import com.zjwly.psoms.domain.vo.EquipmentVO;
import com.zjwly.psoms.domain.vo.EquipmentVersionVO;
import com.zjwly.psoms.enums.EquipmentTypeEnum;
import com.zjwly.psoms.mapper.EquipmentMapper;
import com.zjwly.psoms.service.AttachmentService;
import com.zjwly.psoms.service.EquipmentService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@Service
public class EquipmentServiceImpl extends ServiceImpl<EquipmentMapper, Equipment> implements EquipmentService {
    private final AttachmentService attachmentService;
    private final FastDfsUtil fastDfsUtil;

    @DSTransactional
    @Override
    public void updateInfo(Equipment equipment) {
        // 获取旧数据，将其备份，再更新
        Equipment old = this.getById(equipment.getId());
        equipment.setName(old.getName());
        equipment.setNo(old.getNo());
        equipment.setLotNo(old.getLotNo());
        equipment.setId(null);
        equipment.setProcessType(EquipmentTypeEnum.OMS_UPDATE.name());
        this.save(equipment);
    }

    @Override
    public IPage<EquipmentVO> listOpArea(String name, String no, String type, String approvalStatus, Long current, Long size) {
        IPage<EquipmentVO> page = new Page<>(current, size);
        return this.baseMapper.listOpArea(page, name, no, type, approvalStatus);
    }

    @Override
    public IPage<EquipmentVO> listInfo(String name, String no, String type, Long current, Long size) {
        IPage<EquipmentVO> page = new Page<>(current, size);
        return this.baseMapper.listInfo(page, name, no, type);
    }

    @Override
    public List<EquipmentVersionVO> listVersion(String lotNo) {
        List<EquipmentVersionVO> vos = this.baseMapper.listVersion(lotNo);
        return vos;
    }

    @DSTransactional
    @Override
    public void delete(List<Integer> ids) {
        List<Equipment> equipments = this.listByIds(ids);
        // 删除设备
        this.lambdaUpdate()
            .in(Equipment::getId, ids)
            .or()
            .remove();
    }

    @Override
    public EquipmentVO getDetail(Integer id, String status, String type) {
        return this.baseMapper.getDetail(id, status, type);
    }

    @Override
    public List<Attachment> upload(List<MultipartFile> files) throws IOException {
        List<Attachment> attachments = new ArrayList<>();
        for (MultipartFile file : files) {
            Attachment attachment = new Attachment();
            String path = fastDfsUtil.uploadFile(file);
            // 设备类型
            attachment.setPath(path);
            attachment.setName(file.getOriginalFilename());
            attachments.add(attachment);
        }
        attachmentService.saveBatch(attachments);
        return attachments;
    }

    @Override
    public void saveInfoBatch(List<Equipment> cachedDataList) {
        List<String> names = new ArrayList<>();
        for (Equipment equipment : cachedDataList) {
            // 设置批号
            equipment.setLotNo(IdUtil.simpleUUID());
            // 判断名称唯一性
            String name = equipment.getName();
            Long count = lambdaQuery().eq(Equipment::getName, name).count();
            if (count > 0) {
                names.add(name);
            }
        }
        if (CollUtil.isNotEmpty(names)) {
            throw new ServiceException("以下名称已存在：" + ArrayUtil.join(names, ", "));
        }
        this.saveBatch(cachedDataList);
    }

    @Override
    public void checkName(String name) {
        Long count = lambdaQuery().eq(Equipment::getName, name).count();
        if (count > 0) {
            throw new ServiceException("名称已存在");
        }
    }

    @Override
    public List<Integer> listApprovedIds(String lotNo) {
        return this.baseMapper.listApprovedIds(lotNo);
    }
}
