package com.zjwly.psoms.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zjwly.common.util.BaseChangeLog;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName(value = "om_duty_opl_change_log")
public class DutyOplChangeLog extends BaseChangeLog {

    /**
     * 运行日志Id
     */
    private Integer oplogId;

    /**
     * 运行日志批号
     */
    private String oplogLotNo;

}
