package com.zjwly.psoms.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zjwly.psoms.domain.DutyOperationLog;
import com.zjwly.psoms.domain.vo.*;

import java.util.List;

public interface DutyOperationLogService extends IService<DutyOperationLog> {

    /**
     * 查询列表
     *
     * @param startTime    开始
     * @param endTime      结束
     * @param type         类别
     * @param handoverType 交班值别
     * @param handoverName 交班人
     * @param takeoverType 接班值别
     * @param takeoverName 接班人
     * @param status       状态
     * @param current      当前页
     * @param size         每页记录数
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.zjwly.psoms.domain.vo.DutyOperationLogVO>
     */
    IPage<DutyOperationLogVO> list(String startTime, String endTime, Integer type, Integer handoverType, String handoverName, Integer takeoverType, String takeoverName, Integer status, Integer current, Integer size);

    void updateInfo(DutyOperationLog log);

    void saveInfo(DutyOperationLog log);

    List<ChangeLogVO<DutyOplChangeLogVO>> getChangeLog(String lotNo);

    List<DutyOpVersionVO> listVersion(String lotNo);

    DutyOperationLogVO detail(Integer id);

    List<DutyCalendarVO> getCalendar(String date);
}
