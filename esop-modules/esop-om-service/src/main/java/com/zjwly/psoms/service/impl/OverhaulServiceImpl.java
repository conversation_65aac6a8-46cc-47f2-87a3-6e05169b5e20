package com.zjwly.psoms.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zjwly.psoms.domain.Overhaul;
import com.zjwly.psoms.mapper.OverhaulMapper;
import com.zjwly.psoms.service.OverhaulService;
import org.springframework.stereotype.Service;

@Service
public class OverhaulServiceImpl extends ServiceImpl<OverhaulMapper, Overhaul> implements OverhaulService {

    @Override
    public IPage<Overhaul> listInfo(String name, Integer type, Long current, Long size) {
        IPage<Overhaul> page = new Page<>(current, size);
        return this.lambdaQuery()
                   .like(StrUtil.isNotBlank(name), Overhaul::getEquipmentName, name)
                   .eq(type != null, Overhaul::getType, type).orderByDesc(Overhaul::getId).page(page);
    }
}
