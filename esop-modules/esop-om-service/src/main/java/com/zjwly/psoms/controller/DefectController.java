package com.zjwly.psoms.controller;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zjwly.common.base.Result;
import com.zjwly.common.easyexcel.CustomCellWriteWeightConfig;
import com.zjwly.psoms.domain.Defect;
import com.zjwly.psoms.domain.DefectChangeLog;
import com.zjwly.psoms.domain.vo.ChangeLogVO;
import com.zjwly.psoms.mapper.DefectChangeLogMapper;
import com.zjwly.psoms.service.DefectService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

/**
 * 缺陷管理Controller
 *
 * <AUTHOR>
 * @date 2023/11/01 10:36
 */
@RequestMapping("/ds/om/defect")
@RequiredArgsConstructor
@RestController
public class DefectController {
    private final DefectService defectService;
    private final DefectChangeLogMapper defectChangeLogMapper;

    /**
     * 列表查询
     *
     * @param name   名称
     * @param status 缺陷处理状态 DRAFT：草稿，DEFECT_ENTRY：缺陷录入，DEFECT_ISSUED：缺陷下达，WORK_ASSIGNMENT：工作安排，WORK_COMPLETED：工作完成，ACCEPTANCE：验收合格，DEFECT_CLOSED：缺陷关闭，WORK_ORDER_CANCELED_REQUEST：工单取消申请，EXTENSION_REQUEST：延期申请，WORK_ORDER_CANCELED：工单取消，EXTENSION_APPROVED：延期申请通过
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/11/06 12:08
     */
    @GetMapping("/list")
    public Result list(String name, String status,
                       @RequestParam(value = "current", required = false, defaultValue = "-1") Long current,
                       @RequestParam(value = "size", required = false, defaultValue = "-1") Long size) {
        IPage<Defect> defects = defectService.listInfo(name, status, current, size);
        return Result.ok(defects);
    }

    /**
     * 获取详情
     *
     * @param id
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/11/06 16:14
     */
    @GetMapping("/getDetail")
    public Result getDetail(@RequestParam("id") Integer id) {
        Defect defect = defectService.getById(id);
        return Result.ok(defect);
    }

    /**
     * 新增
     * TODO 加锁
     *
     * @param defect
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/11/01 10:43
     */
    @PostMapping("/save")
    public Result save(@RequestBody Defect defect) {
        defectService.saveInfo(defect);
        return Result.ok();
    }

    /**
     * 修改
     *
     * @param defect
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/11/01 11:01
     */
    @PutMapping("/update")
    public Result update(@RequestBody Defect defect) {
        defectService.updateInfo(defect);
        return Result.ok();
    }

    /**
     * 删除
     *
     * @param id
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/11/01 11:07
     */
    @DeleteMapping("/delete")
    public Result delete(@RequestParam("id") Integer id) {
        defectService.removeInfo(id);
        return Result.ok();
    }

    /**
     * 提交
     *
     * @param defect
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/11/01 13:53
     */
    @PutMapping("/submit")
    public Result submit(@RequestBody Defect defect) {
        defectService.submit(defect);
        return Result.ok();
    }

    /**
     * 确认
     *
     * @param defect
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/11/01 13:55
     */
    @PutMapping("/confirm")
    public Result confirm(@RequestBody Defect defect) {
        defectService.confirm(defect);
        return Result.ok();
    }

    /**
     * 分发
     *
     * @param defect
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/11/01 13:57
     */
    @PutMapping("/distribute")
    public Result distribute(@RequestBody Defect defect) {
        defectService.distribute(defect);
        return Result.ok();
    }

    /**
     * 完成
     *
     * @param defect
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/11/01 13:58
     */
    @PutMapping("/complete")
    public Result complete(@RequestBody Defect defect) {
        defectService.complete(defect);
        return Result.ok();
    }

    /**
     * 验收
     *
     * @param defect
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/11/01 14:01
     */
    @PutMapping("/acceptance")
    public Result acceptance(@RequestBody Defect defect) {
        defectService.acceptance(defect);
        return Result.ok();
    }

    /**
     * 关闭
     *
     * @param defect
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/11/01 14:04
     */
    @PutMapping("/close")
    public Result close(@RequestBody Defect defect) {
        defectService.close(defect);
        return Result.ok();
    }

    /**
     * 取消
     *
     * @param defect
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/11/01 14:15
     */
    @PutMapping("/cancel")
    public Result cancel(@RequestBody Defect defect) {
        defectService.cancel(defect);
        return Result.ok();
    }

    /**
     * 延期申请
     *
     * @param defect
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/11/01 14:28
     */
    @PutMapping("/extensionRequest")
    public Result extensionRequest(@RequestBody Defect defect) {
        defectService.extensionRequest(defect);
        return Result.ok();
    }

    /**
     * 取消审核通过
     *
     * @param defect
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/11/01 14:43
     */
    @PutMapping("/cancelAuditPass")
    public Result cancelAuditPass(@RequestBody Defect defect) {
        defectService.cancelAuditPass(defect);
        return Result.ok();
    }

    /**
     * 延期审核通过
     *
     * @param defect
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/11/01 15:04
     */
    @PutMapping("/extensionAuditPass")
    public Result extensionAuditPass(@RequestBody Defect defect) {
        defectService.extensionAuditPass(defect);
        return Result.ok();
    }

    /**
     * 取消审核驳回
     *
     * @param defect
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/11/01 14:45
     */
    @PutMapping("/cancelAuditReject")
    public Result cancelAuditReject(@RequestBody Defect defect) {
        defectService.cancelAuditReject(defect);
        return Result.ok();
    }

    /**
     * 延期申请驳回
     *
     * @param defect
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/11/01 14:45
     */
    @PutMapping("/extensionAuditReject")
    public Result extensionAuditReject(@RequestBody Defect defect) {
        defectService.extensionAuditReject(defect);
        return Result.ok();
    }

    /**
     * 撤回延期申请
     *
     * @param defect
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/11/01 14:53
     */
    @PutMapping("/extensionAuditWithdraw")
    public Result extensionAuditWithdraw(@RequestBody Defect defect) {
        defectService.extensionAuditWithdraw(defect);
        return Result.ok();
    }

    /**
     * 根据缺陷ID获取缺陷修改日志
     *
     * @param id 缺陷ID
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/11/07 13:53
     * @response {@link Result<List<ChangeLogVO<DefectChangeLog>>>}
     */
    @GetMapping("/getChangeLog")
    public Result getChangeLog(@RequestParam("id") Integer id) {
        List<ChangeLogVO<DefectChangeLog>> changeLogs = defectService.getChangeLog(id);
        return Result.ok(changeLogs);
    }

    /**
     * 导出
     *
     * @param name     设备名称
     * @param status   缺陷处理状态 DRAFT：草稿，DEFECT_ENTRY：缺陷录入，DEFECT_ISSUED：缺陷下达，WORK_ASSIGNMENT：工作安排，WORK_COMPLETED：工作完成，ACCEPTANCE：验收合格，DEFECT_CLOSED：缺陷关闭，WORK_ORDER_CANCELED_REQUEST：工单取消申请，EXTENSION_REQUEST：延期申请，WORK_ORDER_CANCELED：工单取消，EXTENSION_APPROVED：延期申请通过
     * @param current  当前页
     * @param size     每页条数
     * @param response
     * @return void
     * <AUTHOR>
     * @date 2023/11/08 14:11
     */
    @GetMapping("/export")
    public void export(String name, String status,
                       @RequestParam(value = "current", required = false, defaultValue = "-1") Long current,
                       @RequestParam(value = "size", required = false, defaultValue = "-1") Long size,
                       HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("缺陷关联", "UTF-8")
                                    .replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        IPage<Defect> defects = defectService.listInfo(name, status, current, size);
        List<Defect> records = defects.getRecords();
        EasyExcel.write(response.getOutputStream(), Defect.class)
                 .registerWriteHandler(new CustomCellWriteWeightConfig())
                 .sheet()
                 .doWrite(records);
    }
}
