package com.zjwly.psoms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zjwly.common.exception.ServiceException;
import com.zjwly.common.satoken.utils.LoginHelper;
import com.zjwly.common.util.ChangeLogUtil;
import com.zjwly.psoms.domain.Defect;
import com.zjwly.psoms.domain.DefectChangeLog;
import com.zjwly.psoms.domain.vo.ChangeLogVO;
import com.zjwly.psoms.domain.vo.DefectVO;
import com.zjwly.psoms.enums.DefectOpEnum;
import com.zjwly.psoms.enums.DefectStateEnum;
import com.zjwly.psoms.mapper.DefectChangeLogMapper;
import com.zjwly.psoms.mapper.DefectMapper;
import com.zjwly.psoms.service.DefectChangeLogService;
import com.zjwly.psoms.service.DefectService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
public class DefectServiceImpl extends ServiceImpl<DefectMapper, Defect> implements DefectService {
    private final DefectChangeLogService defectChangeLogService;
    private final DefectChangeLogMapper defectChangeLogMapper;

    @DSTransactional
    @Override
    public void updateInfo(Defect defect) {
        Integer id = defect.getId();
        Defect oldDefect = this.getById(id);
        ChangeLogUtil<DefectChangeLog> changeLogUtil = new ChangeLogUtil<DefectChangeLog>() {
            @Override
            protected DefectChangeLog initChangeLog() {
                DefectChangeLog changeLog = new DefectChangeLog();
                changeLog.setDefectId(id);
                changeLog.setClassName(Defect.class.getName());
                changeLog.setOpType(DefectOpEnum.UPDATE);
                return changeLog;
            }
        };
        List<DefectChangeLog> changeLogs = changeLogUtil.compareFields(oldDefect, defect);
        defectChangeLogService.saveBatch(changeLogs);

        this.updateById(defect);
    }

    @DSTransactional
    @Override
    public void saveInfo(Defect defect) {
        // 拼接工单编号
        Defect lastDefect = this.lambdaQuery().orderByDesc(Defect::getId).last("limit 1").one();
        Integer lastSerialNo = 1;
        if (lastDefect != null && lastDefect.getSerialNo() != null) {
            lastSerialNo = lastDefect.getSerialNo();
            lastSerialNo = lastSerialNo + 1;
        }
        String serialNo = String.format("%03d", lastSerialNo);
        String workOrderNo = "QX-" + DateUtil.format(new Date(), DatePattern.SIMPLE_MONTH_PATTERN) + "-" + serialNo;
        defect.setWorkOrderNo(workOrderNo);
        defect.setSerialNo(lastSerialNo);
        this.save(defect);

        DefectChangeLog defectChangeLog = buildLogInstance(defect.getId(), DefectOpEnum.SAVE);
        defectChangeLogService.save(defectChangeLog);
    }

    @Override
    public IPage<Defect> listInfo(String name, String status, Long current, Long size) {
        Integer processingStatus = null;
        if (StrUtil.isNotBlank(status)) {
            processingStatus = DefectStateEnum.getByValue(status).getCode();
        }
        IPage<Defect> page = new Page<>(current, size);
        return this.lambdaQuery().like(StrUtil.isNotBlank(name), Defect::getEquipmentName, name)
                .eq(processingStatus != null, Defect::getProcessingStatus, processingStatus)
                .orderByDesc(Defect::getId)
                .page(page);
    }

    @Override
    public List<ChangeLogVO<DefectChangeLog>> getChangeLog(Integer id) {
        DefectVO defectVO = this.baseMapper.getDetail(id);
        if (defectVO == null) {
            throw new ServiceException("没有对应的缺陷记录！");
        }

        List<ChangeLogVO<DefectChangeLog>> changeLogVOs = new ArrayList<>();
        int index = 1;
        List<DefectChangeLog> clogs = defectChangeLogMapper.listByDefectId(id);
        if (CollUtil.isNotEmpty(clogs)) {
            Map<Date, List<DefectChangeLog>> clogMap = clogs.parallelStream()
                    .collect(Collectors.groupingBy(DefectChangeLog::getCreateTime, LinkedHashMap::new, Collectors.toList()));
            for (Map.Entry<Date, List<DefectChangeLog>> it : clogMap.entrySet()) {
                List<DefectChangeLog> vos = it.getValue();
                DefectChangeLog vo = vos.get(0);
                DefectOpEnum opType = vo.getOpType();
                String log = index + "．" + DateUtil.formatDateTime(it.getKey()) + "，由 " + vo.getCreateByName() + " " + opType.getName() + "。";
                ChangeLogVO<DefectChangeLog> changeLogVO = new ChangeLogVO();
                changeLogVO.setLog(log);
                if (!(opType.equals(DefectOpEnum.SAVE) || opType.equals(DefectOpEnum.WITHDRAW) || opType.equals(DefectOpEnum.DELETE))) {
                    changeLogVO.setDetails(vos);
                }
                changeLogVOs.add(changeLogVO);
                index = index + 1;
            }
        }
        return changeLogVOs;
    }

    @DSTransactional
    @Override
    public void removeInfo(Integer id) {
        DefectChangeLog defectChangeLog = buildLogInstance(id, DefectOpEnum.DELETE);
        defectChangeLogService.save(defectChangeLog);
        this.removeById(id);
    }

    @Override
    public void submit(Defect defect) {
        if (defect.getProcessingStatus() != DefectStateEnum.DRAFT) {
            throw new ServiceException("当前状态不是：" + DefectStateEnum.DRAFT.getDisplayName() + " 不可提交！");
        }
        // 上一个状态
        defect.setPreProcessingStatus(DefectStateEnum.DRAFT);
        // 当前状态
        defect.setProcessingStatus(DefectStateEnum.DEFECT_ENTRY);
        this.updateById(defect);

        DefectChangeLog defectChangeLog = buildLogInstance(defect.getId(), DefectOpEnum.SUBMIT);
        defectChangeLog.setRemark(defect.getRemark());
        defectChangeLogService.save(defectChangeLog);
    }

    @DSTransactional
    @Override
    public void confirm(Defect defect) {
        if (defect.getProcessingStatus() != DefectStateEnum.DEFECT_ENTRY) {
            throw new ServiceException("当前状态不是：" + DefectStateEnum.DEFECT_ENTRY.getDisplayName() + " 不可确认！");
        }
        // 上一个状态
        defect.setPreProcessingStatus(DefectStateEnum.DEFECT_ENTRY);
        // 当前状态
        defect.setProcessingStatus(DefectStateEnum.DEFECT_ISSUED);
        this.updateById(defect);

        DefectChangeLog defectChangeLog = buildLogInstance(defect.getId(), DefectOpEnum.CONFIRM);
        defectChangeLog.setRemark(defect.getRemark());
        defectChangeLogService.save(defectChangeLog);
    }

    @DSTransactional
    @Override
    public void distribute(Defect defect) {
        if (defect.getProcessingStatus() != DefectStateEnum.DEFECT_ISSUED) {
            throw new ServiceException("当前状态不是：" + DefectStateEnum.DEFECT_ISSUED.getDisplayName() + " 不可分发！");
        }
        // 上一个状态
        defect.setPreProcessingStatus(DefectStateEnum.DEFECT_ISSUED);
        // 当前状态
        defect.setProcessingStatus(DefectStateEnum.WORK_ASSIGNMENT);
        defect.setReleaseTime(new Date());
        this.updateById(defect);

        DefectChangeLog defectChangeLog = buildLogInstance(defect.getId(), DefectOpEnum.DISTRIBUTE);
        defectChangeLog.setWorkContent(defect.getWorkContent());
        defectChangeLogService.save(defectChangeLog);
    }

    @DSTransactional
    @Override
    public void complete(Defect defect) {
        if (defect.getProcessingStatus() != DefectStateEnum.WORK_ASSIGNMENT) {
            throw new ServiceException("当前状态不是：" + DefectStateEnum.WORK_ASSIGNMENT.getDisplayName() + " 不可完成！");
        }
        // 上一个状态
        defect.setPreProcessingStatus(DefectStateEnum.WORK_ASSIGNMENT);
        // 当前状态
        defect.setProcessingStatus(DefectStateEnum.WORK_COMPLETED);
        this.updateById(defect);

        DefectChangeLog defectChangeLog = buildLogInstance(defect.getId(), DefectOpEnum.COMPLETE);
        defectChangeLog.setRemark(defect.getRemark());
        defectChangeLogService.save(defectChangeLog);
    }

    @DSTransactional
    @Override
    public void acceptance(Defect defect) {
        if (defect.getProcessingStatus() != DefectStateEnum.WORK_COMPLETED) {
            throw new ServiceException("当前状态不是：" + DefectStateEnum.WORK_COMPLETED.getDisplayName() + " 不可验收！");
        }
        // 上一个状态
        defect.setPreProcessingStatus(DefectStateEnum.WORK_COMPLETED);
        // 当前状态
        defect.setProcessingStatus(DefectStateEnum.ACCEPTANCE);
        this.updateById(defect);

        DefectChangeLog defectChangeLog = buildLogInstance(defect.getId(), DefectOpEnum.ACCEPTANCE);
        defectChangeLog.setRemark(defect.getRemark());
        defectChangeLogService.save(defectChangeLog);
    }

    @DSTransactional
    @Override
    public void close(Defect defect) {
        if (defect.getProcessingStatus() != DefectStateEnum.ACCEPTANCE) {
            throw new ServiceException("当前状态不是：" + DefectStateEnum.ACCEPTANCE.getDisplayName() + " 不可关闭！");
        }
        // 上一个状态
        defect.setPreProcessingStatus(DefectStateEnum.ACCEPTANCE);
        // 当前状态
        defect.setProcessingStatus(DefectStateEnum.DEFECT_CLOSED);
        defect.setCloseTime(new Date());
        this.updateById(defect);

        DefectChangeLog defectChangeLog = buildLogInstance(defect.getId(), DefectOpEnum.CLOSE);
        defectChangeLog.setRemark(defect.getRemark());
        defectChangeLogService.save(defectChangeLog);
    }

    @DSTransactional
    @Override
    public void cancel(Defect defect) {
        // 上一个状态
        defect.setPreProcessingStatus(defect.getProcessingStatus());
        // 当前状态
        defect.setProcessingStatus(DefectStateEnum.WORK_ORDER_CANCELED_REQUEST);
        this.updateById(defect);

        DefectChangeLog defectChangeLog = buildLogInstance(defect.getId(), DefectOpEnum.CANCEL);
        defectChangeLog.setRemark(defect.getRemark());
        defectChangeLogService.save(defectChangeLog);
    }

    @DSTransactional
    @Override
    public void extensionRequest(Defect defect) {
        // 上一个状态
        defect.setPreProcessingStatus(defect.getProcessingStatus());
        // 当前状态
        defect.setProcessingStatus(DefectStateEnum.EXTENSION_REQUEST);
        this.updateById(defect);
        DefectChangeLog defectChangeLog = buildLogInstance(defect.getId(), DefectOpEnum.EXTENSION_REQUEST);
        defectChangeLog.setExtensionReason(defect.getExtensionReson());
        defectChangeLog.setExtensionTime(defect.getExtensionTime());
        defectChangeLogService.save(defectChangeLog);
    }

    @DSTransactional
    @Override
    public void cancelAuditPass(Defect defect) {
        if (defect.getProcessingStatus() != DefectStateEnum.WORK_ORDER_CANCELED_REQUEST) {
            throw new ServiceException("当前状态不是：" + DefectStateEnum.WORK_ORDER_CANCELED_REQUEST
                    .getDisplayName() + " 不可通过！");
        }
        // 上一个状态
        defect.setPreProcessingStatus(defect.getPreProcessingStatus());
        // 当前状态
        defect.setProcessingStatus(DefectStateEnum.WORK_ORDER_CANCELED);
        this.updateById(defect);

        DefectChangeLog defectChangeLog = buildLogInstance(defect.getId(), DefectOpEnum.CANCEL_PASSW);
        defectChangeLog.setRemark(defect.getRemark());
        defectChangeLogService.save(defectChangeLog);
    }

    @DSTransactional
    @Override
    public void extensionAuditPass(Defect defect) {
        if (defect.getProcessingStatus() != DefectStateEnum.EXTENSION_REQUEST) {
            throw new ServiceException("当前状态不是：" + DefectStateEnum.EXTENSION_REQUEST.getDisplayName() + " 不可通过！");
        }
        // 当前状态
        DefectStateEnum preProcessingStatus = defect.getPreProcessingStatus();
        defect.setProcessingStatus(preProcessingStatus);
        // 上一个状态
        defect.setPreProcessingStatus(DefectStateEnum.EXTENSION_APPROVED);
        // 延期审核通过，设置为延期状态
        defect.setIsExtensionPass(1);
        this.updateById(defect);

        DefectChangeLog defectChangeLog = buildLogInstance(defect.getId(), DefectOpEnum.EXTENSION_PASS);
        defectChangeLog.setRemark(defect.getRemark());
        defectChangeLogService.save(defectChangeLog);
    }

    @DSTransactional
    @Override
    public void cancelAuditReject(Defect defect) {
        if (defect.getProcessingStatus() != DefectStateEnum.WORK_ORDER_CANCELED_REQUEST) {
            throw new ServiceException("当前状态不是：" + DefectStateEnum.WORK_ORDER_CANCELED_REQUEST.getDisplayName() + " 不可驳回！");
        }
        // 上一个状态
        DefectStateEnum preProcessingStatus = defect.getPreProcessingStatus();
        defect.setPreProcessingStatus(DefectStateEnum.WORK_ORDER_CANCELED_REQUEST);
        // 驳回后状态设置为申请前上一步状态
        defect.setProcessingStatus(preProcessingStatus);
        this.updateById(defect);

        DefectChangeLog defectChangeLog = buildLogInstance(defect.getId(), DefectOpEnum.CANCEL_REJECT);
        defectChangeLog.setRemark(defect.getRemark());
        defectChangeLogService.save(defectChangeLog);
    }

    @DSTransactional
    @Override
    public void extensionAuditReject(Defect defect) {
        if (defect.getProcessingStatus() != DefectStateEnum.EXTENSION_REQUEST) {
            throw new ServiceException("当前状态不是：" + DefectStateEnum.EXTENSION_REQUEST.getDisplayName() + " 不可驳回！");
        }
        // 上一个状态
        DefectStateEnum preProcessingStatus = defect.getPreProcessingStatus();
        defect.setPreProcessingStatus(DefectStateEnum.EXTENSION_REQUEST);
        // 驳回后状态设置为申请前上一步状态
        defect.setProcessingStatus(preProcessingStatus);
        this.updateById(defect);

        DefectChangeLog defectChangeLog = buildLogInstance(defect.getId(), DefectOpEnum.EXTENSION_REJECT);
        defectChangeLog.setRemark(defect.getRemark());
        defectChangeLogService.save(defectChangeLog);
    }

    @DSTransactional
    @Override
    public void extensionAuditWithdraw(Defect defect) {
        if (defect.getProcessingStatus() != DefectStateEnum.EXTENSION_REQUEST) {
            throw new ServiceException("当前状态不是：" + DefectStateEnum.EXTENSION_REQUEST.getDisplayName() + " 不可撤回！");
        }
        // 上一个状态
        DefectStateEnum preProcessingStatus = defect.getPreProcessingStatus();
        defect.setPreProcessingStatus(DefectStateEnum.EXTENSION_REQUEST);
        // 撤回后，当前状态为申请前上一步状态
        defect.setProcessingStatus(preProcessingStatus);
        this.updateById(defect);

        DefectChangeLog defectChangeLog = buildLogInstance(defect.getId(), DefectOpEnum.WITHDRAW);
        defectChangeLogService.save(defectChangeLog);
    }

    private static DefectChangeLog buildLogInstance(Integer id, DefectOpEnum defectOpEnum) {
        Long userId = LoginHelper.getUserId();
        Date now = new Date();
        DefectChangeLog defectChangeLog = new DefectChangeLog();
        defectChangeLog.setDefectId(id);
        defectChangeLog.setCreateBy(userId.intValue());
        defectChangeLog.setCreateTime(now);
        defectChangeLog.setUpdateBy(userId.intValue());
        defectChangeLog.setUpdateTime(now);
        defectChangeLog.setOpType(defectOpEnum);
        return defectChangeLog;
    }

}
