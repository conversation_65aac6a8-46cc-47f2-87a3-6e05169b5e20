package com.zjwly.psoms.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zjwly.common.annotation.ChangeLog;
import com.zjwly.common.mybatis.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
    * 运行日志
    */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "om_duty_operation_log")
@EqualsAndHashCode(callSuper = true)
public class DutyOperationLog extends BaseEntity {
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 名称
     */
    private String name;

    /**
     * 批号
     */
    @TableField(value = "lot_no")
    private String lotNo;

    /**
     * 版本
     */
    @TableField(value = "version")
    private Integer version;

    /**
     * 日志日期
     */
    @TableField(value = "op_time")
    private Date opTime;

    /**
     * 类别 0：白班，1：中班，2：夜班
     */
    @TableField(value = "op_type")
    private Integer opType;

    /**
     * 交班人
     */
    @TableField(value = "handover_by")
    private Long handoverBy;

    /**
     * 交班时间
     */
    @TableField(value = "handover_time")
    private Date handoverTime;

    /**
     * 交班值别
     */
    @TableField(value = "handover_duty")
    private Integer handoverDuty;

    /**
     * 接班人
     */
    @TableField(value = "takeover_by")
    private Long takeoverBy;

    /**
     * 接班时间
     */
    @TableField(value = "takeover_time")
    private Date takeoverTime;

    /**
     * 接班值别
     */
    @TableField(value = "takeover_duty")
    private Integer takeoverDuty;

    /**
     * 运行方式
     */
    @ChangeLog
    @TableField(value = "op_mode")
    private String opMode;

    /**
     * 调度指令
     */
    @ChangeLog
    @TableField(value = "sched_order")
    private String schedOrder;

    /**
     * 领导指示
     */
    @ChangeLog
    @TableField(value = "leader_instr")
    private String leaderInstr;

    /**
     * 两票记事
     */
    @ChangeLog
    @TableField(value = "two_ticket_note")
    private String twoTicketNote;

    /**
     * 缺陷及异常
     */
    @ChangeLog
    @TableField(value = "defect_abnormal")
    private String defectAbnormal;

    /**
     * 运行记事
     */
    @ChangeLog
    @TableField(value = "op_note")
    private String opNote;

    /**
     * 地刀、地线登记
     */
    @ChangeLog
    @TableField(value = "grd_knife_wire")
    private String grdKnifeWire;

    /**
     * 状态 0：待接班，1：确认接班，2：待确认
     */
    @TableField(value = "op_status")
    private Integer opStatus;
}
