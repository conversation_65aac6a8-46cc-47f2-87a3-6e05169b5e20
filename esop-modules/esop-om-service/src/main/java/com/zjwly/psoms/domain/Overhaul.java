package com.zjwly.psoms.domain;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zjwly.common.mybatis.core.domain.BaseEntity;
import com.zjwly.psoms.converter.OverhaulTypeConverter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 检修台账
 */
@ExcelIgnoreUnannotated
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "om_overhaul")
public class Overhaul extends BaseEntity {
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 设备ID
     */
    @TableField(value = "equipment_id")
    private Integer equipmentId;

    /**
     * 设备批号
     */
    @TableField(value = "equipment_lot_no")
    private String equipmentLotNo;

    /**
     * 设备名称
     */
    @ExcelProperty("设备名称")
    @TableField(value = "equipment_name")
    private String equipmentName;

    /**
     * 设备编号
     */
    @TableField(value = "equipment_no")
    private String equipmentNo;

    /**
     * 涉及容量（MW）
     */
    @ExcelProperty("涉及容量（MW）")
    @TableField(value = "capacity")
    private BigDecimal capacity;

    /**
     * 检修类别 1：A类，2：B类，3：C类
     */
    @ExcelProperty(value = "检修类别", converter = OverhaulTypeConverter.class)
    @TableField(value = "`type`")
    private Integer type;

    /**
     * 开工时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat("yyyy-MM-dd")
    @ExcelProperty("开工日期")
    @TableField(value = "start_date")
    private Date startDate;

    /**
     * 完工时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat("yyyy-MM-dd")
    @ExcelProperty("完工时间")
    @TableField(value = "completion_date")
    private Date completionDate;

    /**
     * 检修天数
     */
    @ExcelProperty("检修天数")
    @TableField(value = "`days`")
    private Integer days;

    /**
     * 进行的主要项目
     */
    @ExcelProperty("进行的主要项目")
    @TableField(value = "main_project")
    private String mainProject;

    /**
     * 超标工期说明
     */
    @ExcelProperty("超标工期说明")
    @TableField(value = "overrun_description")
    private String overrunDescription;

    /**
     * 备注
     */
    @ExcelProperty("备注")
    @TableField(value = "remark")
    private String remark;

}
