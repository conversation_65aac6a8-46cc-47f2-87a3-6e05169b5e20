package com.zjwly.psoms.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zjwly.psoms.domain.Duty;
import com.zjwly.psoms.domain.vo.DutyRecordVO;
import com.zjwly.psoms.domain.vo.DutyVO;

import java.util.List;

public interface DutyService extends IService<Duty>{


    List<DutyVO> list(String startTime, String endTime);

    void enabled(Integer id, Integer enabled);

    IPage<DutyRecordVO> dutyRecord(String startTime, String endTime, Long current, Long size);

    /**
     * 根据用户ID获取值别
     * <AUTHOR>
     * @date 2023/10/13 09:38
     * @param userId
     * @return int
     */
    int getDutyTypeByUserId(Integer userId);
}
