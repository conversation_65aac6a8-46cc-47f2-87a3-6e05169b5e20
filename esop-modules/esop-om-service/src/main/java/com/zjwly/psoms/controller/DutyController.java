package com.zjwly.psoms.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zjwly.common.base.Result;
import com.zjwly.psoms.domain.Duty;
import com.zjwly.psoms.domain.vo.DutyRecordVO;
import com.zjwly.psoms.domain.vo.DutyVO;
import com.zjwly.psoms.service.DutyService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 值别安排
 *
 * <AUTHOR>
 * @date 2023/10/10 14:10
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/ds/om/duty")
public class DutyController {
    private final DutyService dutyService;

    /**
     * 查询列表
     *
     * @param startTime
     * @param endTime
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/10/10 14:49
     */
    @GetMapping("/list")
    public Result list(String startTime, String endTime) {
        List<DutyVO> vos = dutyService.list(startTime, endTime);
        return Result.ok(vos);
    }

    /**
     * 创建
     *
     * @param duty
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/10/10 14:54
     */
    @PostMapping("/save")
    public Result save(@RequestBody Duty duty) {
        dutyService.save(duty);
        return Result.ok();
    }

    /**
     * 启停用
     *
     * @param id      ID
     * @param enabled 0：停用，1：启用
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/10/10 14:58
     */
    @PutMapping("/enabled/{id}/{enabled}")
    public Result enabled(@PathVariable("id") Integer id, @PathVariable("enabled") Integer enabled) {
        dutyService.enabled(id, enabled);
        return Result.ok();
    }

    /**
     * 值别记录
     *
     * @param startTime
     * @param endTime
     * @param current
     * @param size
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/10/11 11:01
     */
    @GetMapping("/dutyRecord")
    public Result dutyRecord(@RequestParam(value = "startTime", required = false) String startTime,
                             @RequestParam(value = "endTime", required = false) String endTime,
                             @RequestParam(value = "current", required = false, defaultValue = "-1") Long current,
                             @RequestParam(value = "size", required = false, defaultValue = "-1") Long size) {
        IPage<DutyRecordVO> voPage = dutyService.dutyRecord(startTime, endTime, current, size);
        return Result.ok(voPage);
    }

    /**
     * 根据用户ID获取值别
     *
     * @param userId
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/10/13 10:27
     */
    @GetMapping("/getDutyTypeByUserId")
    public Result getDutyTypeByUserId(@RequestParam(value = "userId", required = true) Integer userId) {
        int dutyType = dutyService.getDutyTypeByUserId(userId);
        return Result.ok(dutyType);
    }
}
