package com.zjwly.psoms.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zjwly.psoms.domain.PeriodicPlan;
import com.zjwly.psoms.domain.vo.PeriodicPlanVO;
import com.zjwly.psoms.enums.PeriodicPlanCycleTypeEnum;
import com.zjwly.psoms.enums.PeriodicPlanTypeEnum;
import com.zjwly.psoms.mapper.PeriodicPlanMapper;
import com.zjwly.psoms.service.PeriodicPlanService;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class PeriodicPlanServiceImpl extends ServiceImpl<PeriodicPlanMapper, PeriodicPlan> implements PeriodicPlanService {

    @Override
    public void saveInfo(PeriodicPlan periodicPlan) {
        periodicPlan.setLotNo(IdUtil.simpleUUID());
        Integer cycleType = periodicPlan.getCycleType();
        if (PeriodicPlanCycleTypeEnum.WEEK.getCode() == cycleType) {
            Date createTime = periodicPlan.getCreateTime();
            int weekNo = DateUtil.weekOfMonth(createTime);
            periodicPlan.setWeekNo(weekNo);
        }
        this.save(periodicPlan);
    }

    @Override
    public void updateInfo(PeriodicPlan periodicPlan) {
        Integer id = periodicPlan.getId();
        PeriodicPlan oldPeriodPlan = this.getById(id);
        String lotNo = oldPeriodPlan.getLotNo();

        periodicPlan.setType(PeriodicPlanTypeEnum.UPDATED.getCode());
        periodicPlan.setLotNo(lotNo);
        periodicPlan.setId(null);
        this.save(periodicPlan);
    }

    @Override
    public IPage<PeriodicPlanVO> listInfo(DateTime startTime, DateTime endTime, Integer cycleType, Integer status,
                                          String taskContent, Integer current, Integer size) {
        IPage<PeriodicPlanVO> page = new Page<>(current, size);
        return this.baseMapper.listInfo(page, startTime, endTime, cycleType, status, taskContent);
    }

    @Override
    public IPage<PeriodicPlanVO> listEditRecord(String lotNo, Integer current, Integer size) {
        IPage<PeriodicPlanVO> page = new Page<>(current, size);
        return this.baseMapper.listEditRecord(page, lotNo);
    }
}
