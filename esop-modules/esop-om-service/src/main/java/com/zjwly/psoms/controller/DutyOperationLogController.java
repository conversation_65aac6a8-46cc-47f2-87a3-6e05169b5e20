package com.zjwly.psoms.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zjwly.common.base.Result;
import com.zjwly.common.satoken.utils.LoginHelper;
import com.zjwly.psoms.domain.DutyOperationLog;
import com.zjwly.psoms.domain.vo.*;
import com.zjwly.psoms.enums.DutyOpStatusEnum;
import com.zjwly.psoms.service.DutyOperationLogService;
import com.zjwly.psoms.service.DutyService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 运行日志Controller
 *
 * <AUTHOR>
 * @date 2023/10/13 10:21
 */
@RestController
@RequestMapping("/ds/om/operationLog")
@RequiredArgsConstructor
public class DutyOperationLogController {
    private final DutyOperationLogService dutyOperationLogService;
    private final DutyService dutyService;

    /**
     * 新建
     *
     * @param log
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/10/13 13:57
     */
    @PostMapping("/save")
    public Result save(@RequestBody DutyOperationLog log) {
        checkUserDuty();
        dutyOperationLogService.saveInfo(log);
        return Result.ok();
    }

    /**
     * 检查当前用户是否安排值别并启用
     *
     * @return void
     * <AUTHOR>
     * @date 2023/10/24 14:52
     */
    private void checkUserDuty() {
        Long userId = LoginHelper.getUserId();
        dutyService.getDutyTypeByUserId(Math.toIntExact(userId));
    }

    @PutMapping("/update")
    public Result update(@RequestBody DutyOperationLog log) {
        checkUserDuty();
        DutyOperationLog old = dutyOperationLogService.getById(log.getId());
        if (old.getOpStatus() == DutyOpStatusEnum.CONFIRMED.getCode()) {
            log.setOpStatus(DutyOpStatusEnum.AWAITING_CONFIRMATION.getCode());
        }
        dutyOperationLogService.updateInfo(log);
        return Result.ok();
    }

    /**
     * 查询列表
     *
     * @param startTime    开始
     * @param endTime      结束
     * @param type         类别
     * @param handoverType 交班值别
     * @param handoverName 交班人
     * @param takeoverType 接班值别
     * @param takeoverName 接班人
     * @param status       状态
     * @param current      当前页
     * @param size         每页记录数
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/10/13 11:39
     */
    @GetMapping("/list")
    public Result list(String startTime, String endTime, Integer type, Integer handoverType, String handoverName,
                       Integer takeoverType, String takeoverName, Integer status,
                       @RequestParam(value = "current", required = false, defaultValue = "-1") Integer current,
                       @RequestParam(value = "size", required = false, defaultValue = "-1") Integer size) {
        IPage<DutyOperationLogVO> pageVo = dutyOperationLogService.list(startTime, endTime, type, handoverType, handoverName, takeoverType, takeoverName, status, current, size);
        return Result.ok(pageVo);
    }

    /**
     * 根据批号获取修改记录
     *
     * @param lotNo
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/10/13 18:47
     */
    @GetMapping("/getChangeLog")
    public Result getChangeLog(@RequestParam(value = "lotNo") String lotNo) {
        List<ChangeLogVO<DutyOplChangeLogVO>> changeLogs = dutyOperationLogService.getChangeLog(lotNo);
        return Result.ok(changeLogs);
    }

    /**
     * 根据批号获取版本列表
     *
     * @param lotNo
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/10/13 18:57
     */
    @GetMapping("/listVersion")
    public Result listVersion(@RequestParam(value = "lotNo") String lotNo) {
        List<DutyOpVersionVO> versionVOs = dutyOperationLogService.listVersion(lotNo);
        return Result.ok(versionVOs);
    }

    /**
     * 根据ID获取详情
     *
     * @param id
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/10/13 19:00
     */
    @GetMapping("/detail")
    public Result detail(@RequestParam("id") Integer id) {
        DutyOperationLogVO vo = dutyOperationLogService.detail(id);
        return Result.ok(vo);
    }

    /**
     * 获取当前日期一周的日历
     *
     * @param date
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/10/13 19:59
     */
    @GetMapping("/getCalendar")
    public Result calendar(String date) {
        List<DutyCalendarVO> vos = dutyOperationLogService.getCalendar(date);
        return Result.ok(vos);
    }

    /**
     * 接班/确认
     *
     * @param lotNo
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/10/13 20:30
     */
    @PutMapping("/takeover/{lotNo}")
    public Result takeover(@PathVariable("lotNo") String lotNo) {
        DutyOperationLog operationLog = dutyOperationLogService.lambdaQuery().eq(DutyOperationLog::getLotNo, lotNo)
                                                               .orderByDesc(DutyOperationLog::getId)
                                                               .last("limit 1").one();
        Long userId = LoginHelper.getUserId();
        int duty = dutyService.getDutyTypeByUserId(Math.toIntExact(userId));
        dutyOperationLogService.lambdaUpdate()
                               .eq(DutyOperationLog::getLotNo, lotNo)
                               .set(DutyOperationLog::getOpStatus, DutyOpStatusEnum.CONFIRMED.getCode())
                               .set(DutyOperationLog::getTakeoverBy, userId)
                               .set(DutyOperationLog::getTakeoverTime, operationLog.getHandoverTime())
                               .set(DutyOperationLog::getTakeoverDuty, duty)
                               .update();
        return Result.ok();
    }

    /**
     * 撤回
     *
     * @param lotNo
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/10/13 20:34
     */
    @PutMapping("/recall/{lotNo}")
    public Result recall(@PathVariable("lotNo") String lotNo) {
        checkUserDuty();
        dutyOperationLogService.lambdaUpdate()
                               .eq(DutyOperationLog::getLotNo, lotNo)
                               .set(DutyOperationLog::getOpStatus, DutyOpStatusEnum.AWAITING_TAKEOVER.getCode())
                               .set(DutyOperationLog::getTakeoverBy, null)
                               .set(DutyOperationLog::getTakeoverDuty, null)
                               .set(DutyOperationLog::getTakeoverTime, null)
                               .update();
        return Result.ok();
    }

    /**
     * 删除
     *
     * @param lotNo
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/10/13 20:35
     */
    @DeleteMapping("/delete/{lotNo}")
    public Result delete(@PathVariable("lotNo") String lotNo) {
        checkUserDuty();
        dutyOperationLogService.lambdaUpdate()
                               .eq(DutyOperationLog::getLotNo, lotNo)
                               .remove();
        return Result.ok();
    }
}
