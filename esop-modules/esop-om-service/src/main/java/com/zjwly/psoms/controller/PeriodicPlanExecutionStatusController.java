package com.zjwly.psoms.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zjwly.common.base.Result;
import com.zjwly.psoms.domain.PeriodicPlanExecutionStatus;
import com.zjwly.psoms.domain.vo.PeriodicPlanExecutionStatusVO;
import com.zjwly.psoms.scheduler.ScheduledTask;
import com.zjwly.psoms.service.PeriodicPlanExecutionStatusService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 定期工作执行情况Controller
 *
 * <AUTHOR>
 * @date 2023/10/18 14:25
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/ds/om/ppExecutionStatus")
public class PeriodicPlanExecutionStatusController {
    private final PeriodicPlanExecutionStatusService executionStatusService;
    private final ScheduledTask scheduledTask;

    /**
     * 填报
     *
     * @param executionStatus
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/10/18 15:15
     */
    @PutMapping("/update")
    public Result update(@RequestBody PeriodicPlanExecutionStatus executionStatus) {
        executionStatusService.updateInfo(executionStatus);
        return Result.ok();
    }

    /**
     * 查询列表
     *
     * @param startDate   开始
     * @param endDate     结束
     * @param shiftType   班类型 0：白班，1：中班，2：夜班
     * @param status      填报状态 0：未完成，1：已完成
     * @param executorId  执行人ID
     * @param taskContent 执行任务
     * @param cycleType   周期类型 0：班，1：日，2：周，3：月，4：季度，5：年
     * @param current
     * @param size
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/10/18 15:11
     */
    @GetMapping("/list/{cycleType}")
    public Result list(String startDate, String endDate, Integer shiftType, Integer status,
                       Long executorId, String taskContent,
                       @PathVariable(value = "cycleType", required = true) Integer cycleType,
                       @RequestParam(value = "current", required = false, defaultValue = "-1") Integer current,
                       @RequestParam(value = "size", required = false, defaultValue = "-1") Integer size) {
        IPage<PeriodicPlanExecutionStatusVO> voPage = executionStatusService.listInfo(cycleType, startDate, endDate, shiftType, status, executorId, taskContent, current, size);
        return Result.ok(voPage);
    }

    @GetMapping("/recallTask")
    public Result recallTask(@RequestParam("date") String date, @RequestParam("status") Integer status) {
        scheduledTask.setDate(date);
        scheduledTask.setStatus(status);
        scheduledTask.createExecutionStaus();
        return Result.ok();
    }
}
