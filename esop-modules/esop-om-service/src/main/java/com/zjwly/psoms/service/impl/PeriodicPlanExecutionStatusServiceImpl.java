package com.zjwly.psoms.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zjwly.psoms.domain.PeriodicPlanExecutionStatus;
import com.zjwly.psoms.domain.vo.PeriodicPlanExecutionStatusVO;
import com.zjwly.psoms.mapper.PeriodicPlanExecutionStatusMapper;
import com.zjwly.psoms.service.PeriodicPlanExecutionStatusService;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class PeriodicPlanExecutionStatusServiceImpl extends ServiceImpl<PeriodicPlanExecutionStatusMapper, PeriodicPlanExecutionStatus> implements PeriodicPlanExecutionStatusService {

    @Override
    public void updateInfo(PeriodicPlanExecutionStatus executionStatus) {
        executionStatus.setStatus(1);
        executionStatus.setFillTime(new Date());
        this.updateById(executionStatus);
    }

    @Override
    public IPage<PeriodicPlanExecutionStatusVO> listInfo(Integer cycleType, String startDate, String endDate,
                                                         Integer shiftType, Integer status, Long executorId,
                                                         String taskContent, Integer current, Integer size) {
        IPage<PeriodicPlanExecutionStatusVO> page = new Page<>(current, size);
        return this.baseMapper.listInfo(page, cycleType, startDate, endDate, shiftType, status, executorId, taskContent);
    }
}
