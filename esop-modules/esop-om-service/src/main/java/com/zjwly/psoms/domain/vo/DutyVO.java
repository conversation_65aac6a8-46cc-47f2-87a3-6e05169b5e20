package com.zjwly.psoms.domain.vo;

import com.zjwly.psoms.domain.Duty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 值别安排VO
 *
 * <AUTHOR>
 * @date 2023/10/10 14:40
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class DutyVO extends Duty {
    /**
     * 一值用户名称
     */
    private String duty1Name;

    /**
     * 二值用户名称
     */
    private String duty2Name;

    /**
     * 三值用户名称
     */
    private String duty3Name;

    /**
     * 四值用户名称
     */
    private String duty4Name;

    /**
     * 五值用户名称
     */
    private String duty5Name;
}
