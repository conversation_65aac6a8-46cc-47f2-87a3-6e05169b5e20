package com.zjwly.psoms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zjwly.common.exception.ServiceException;
import com.zjwly.psoms.domain.Duty;
import com.zjwly.psoms.domain.DutyRecord;
import com.zjwly.psoms.domain.vo.DutyRecordVO;
import com.zjwly.psoms.domain.vo.DutyVO;
import com.zjwly.psoms.mapper.DutyMapper;
import com.zjwly.psoms.mapper.DutyRecordMapper;
import com.zjwly.psoms.service.DutyRecordService;
import com.zjwly.psoms.service.DutyService;
import com.zjwly.system.domain.vo.SysUserVo;
import com.zjwly.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
public class DutyServiceImpl extends ServiceImpl<DutyMapper, Duty> implements DutyService {
    private final DutyRecordService dutyRecordService;
    private final DutyRecordMapper dutyRecordMapper;
    private final ISysUserService sysUserService;

    @Override
    public List<DutyVO> list(String startTime, String endTime) {
        List<DutyVO> vos = this.baseMapper.listInfo(startTime, endTime);
        return vos;
    }

    @DSTransactional
    @Override
    public void enabled(Integer id, Integer enabled) {
        Duty duty = this.getById(id);

        if (duty == null) {
            throw new ServiceException("当前值别记录不存在！");
        }

        if (duty.getEnabled() == 0 && enabled == 0 || duty.getEnabled() == 1 && enabled == 1) {
            throw new ServiceException("当前值别启用状态未发生变化！");
        }

        // 历史值班安排表，重新启用时，如有人已失去权限，弹窗提示，当前启用不生效
        Set<Long> userIds = new HashSet<>();
        for (int i = 1; i <= 5; i++) {
            String fieldName = "duty" + i;
            String value = (String) ReflectUtil.getFieldValue(duty, fieldName);
            if (StrUtil.isNotBlank(value)) {
                String[] uids = value.split(",");
                if (ArrayUtil.isNotEmpty(uids)) {
                    for (String uid : uids) {
                        userIds.add(Long.valueOf(uid));
                    }
                }
            }
        }

        List<SysUserVo> userVos = sysUserService.selectUserByIds(null, null);
        List<Long> allUserIds = userVos.parallelStream()
            .map(SysUserVo::getUserId)
            .collect(Collectors.toList());

        List<Long> subtracts = CollUtil.subtractToList(userIds, allUserIds);
        if (CollUtil.isNotEmpty(subtracts)) {
            List<SysUserVo> sysUsers = sysUserService.selectUserByIds(subtracts, null);
            if (CollUtil.isNotEmpty(sysUsers)) {
                Map<Long, SysUserVo> userMap = sysUsers.parallelStream()
                    .collect(Collectors.toMap(SysUserVo::getUserId, Function.identity(), (key1, key2) -> key2));
                List<String> nickNames = new ArrayList<>();
                for (Long subtract : subtracts) {
                    SysUserVo user = userMap.get(subtract);
                    if (user != null) {
                        String nickName = user.getNickName();
                        nickNames.add(nickName);
                    }
                }
                String userNameJoin = CollUtil.join(nickNames, "、");
                throw new ServiceException(userNameJoin + "值班权限失效，请重新分配人员！");
            }
        }
        Date now = new Date();

        if (enabled == 1) {
            // 更新生效时间
            duty.setEffectiveTime(now);

            // 保证只有一个生效的值别安排：
            // 1、将其他值别安排修改为失效状态
            // 2、查出其他值别关联安排记录中未设置失效时间的结果，更新失效时间
            List<Duty> dutys = this.lambdaQuery()
                .eq(Duty::getEnabled, 1)
                .list();
            if (CollUtil.isNotEmpty(dutys)) {
                // 所有启用状态的值别安排ID集合
                List<Integer> ids = dutys.parallelStream()
                    .map(Duty::getId)
                    .collect(Collectors.toList());
                this.lambdaUpdate()
                    .in(Duty::getId, ids)
                    .set(Duty::getEnabled, 0)
                    .set(Duty::getExpirationTime, now)
                    .update();
                dutyRecordService.lambdaUpdate()
                    .in(DutyRecord::getDutyId, ids)
                    .isNull(DutyRecord::getExpirationTime)
                    .set(DutyRecord::getExpirationTime, now)
                    .update();
            }
            // 新增值别安排记录：
            // 1、将原有值别安排记录中的生效数据切未设置失效时间的记录查出，设置为失效
            // 2、新增一条生效值别安排记录
            dutyRecordService.lambdaUpdate()
                .eq(DutyRecord::getDutyId, duty.getId())
                .isNull(DutyRecord::getExpirationTime)
                .set(DutyRecord::getExpirationTime, now)
                .update();
            DutyRecord dutyRecord = new DutyRecord();
            dutyRecord.setDutyId(id);
            dutyRecord.setEffectiveTime(now);
            dutyRecordService.save(dutyRecord);
        } else {
            // 更新失效时间
            duty.setExpirationTime(now);

            // 更新值别安排记录失效时间
            dutyRecordService.lambdaUpdate()
                .eq(DutyRecord::getDutyId, duty.getId())
                .isNull(DutyRecord::getExpirationTime)
                .set(DutyRecord::getExpirationTime, now)
                .update();
        }

        duty.setEnabled(enabled);
        this.updateById(duty);
    }

    @Override
    public IPage<DutyRecordVO> dutyRecord(String startTime, String endTime, Long current, Long size) {
        IPage<DutyRecordVO> page = new Page<>(current, size);
        return dutyRecordMapper.selectPpageInfo(page, startTime, endTime);
    }

    @Override
    public int getDutyTypeByUserId(Integer userId) {
        Duty duty = this.lambdaQuery()
            .eq(Duty::getEnabled, 1)
            .orderByDesc(Duty::getEffectiveTime)
            .last("limit 1")
            .one();

        if (duty == null) {
            throw new ServiceException("值别安排未启用！");
        }

        for (int i = 1; i <= 5; i++) {
            String fieldName = "duty" + i;
            String value = (String) ReflectUtil.getFieldValue(duty, fieldName);
            String[] uids = value.split(",");
            String uidStr = userId.toString();
            if (ArrayUtil.contains(uids, uidStr)) {
                return i;
            }
        }

        throw new ServiceException("用户未关联值别！");
    }

}
