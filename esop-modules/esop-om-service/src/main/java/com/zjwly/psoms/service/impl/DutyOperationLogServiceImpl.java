package com.zjwly.psoms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zjwly.common.exception.ServiceException;
import com.zjwly.common.util.ChangeLogUtil;
import com.zjwly.psoms.domain.DutyOperationLog;
import com.zjwly.psoms.domain.DutyOplChangeLog;
import com.zjwly.psoms.domain.vo.*;
import com.zjwly.psoms.mapper.DutyOperationLogMapper;
import com.zjwly.psoms.mapper.DutyOplChangeLogMapper;
import com.zjwly.psoms.service.DutyOperationLogService;
import com.zjwly.psoms.service.DutyOplChangeLogService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
public class DutyOperationLogServiceImpl extends ServiceImpl<DutyOperationLogMapper, DutyOperationLog> implements DutyOperationLogService {
    private final DutyOplChangeLogService dutyOplChangeLogService;
    private final DutyOplChangeLogMapper dutyOplChangeLogMapper;
    private final DutyOperationLogMapper dutyOperationLogMapper;

    @Override
    public IPage<DutyOperationLogVO> list(String startTime, String endTime, Integer type, Integer handoverDuty,
                                          String handoverName, Integer takeoverDuty, String takeoverName,
                                          Integer status, Integer current, Integer size) {
        IPage<DutyOperationLogVO> page = new Page<>(current, size);
        return this.baseMapper.listPage(page, startTime, endTime, type, handoverDuty, handoverName, takeoverDuty, takeoverName, status);
    }

    @Override
    public void saveInfo(DutyOperationLog log) {
        // 用户选择的交班时间，同时作为接班时间，包含时分秒
        Date opTimeReq = log.getOpTime();
        log.setHandoverTime(opTimeReq);

        // 保存的交班日期，只保留年月日
        String formatDate = DateUtil.formatDate(opTimeReq);
        log.setName("运行日志（" + formatDate + "）");
        DateTime opTime = DateUtil.parseDate(formatDate);
        log.setOpTime(opTime);

        Integer opType = log.getOpType();
        Long count = this.lambdaQuery()
                            .eq(DutyOperationLog::getOpTime, opTime)
                            .eq(DutyOperationLog::getOpType, opType)
                            .count();
        if (count > 0) {
            throw new ServiceException("当前日期已经存在此类型，请勿重复创建！");
        }

        // 批号
        log.setLotNo(IdUtil.simpleUUID());
        this.save(log);
    }

    @Override
    public List<ChangeLogVO<DutyOplChangeLogVO>> getChangeLog(String lotNo) {
        List<DutyOperationLogVO> logVOS = dutyOperationLogMapper.listByLotNo(lotNo);
        if (CollUtil.isEmpty(logVOS)) {
            throw new ServiceException("没有对应的运行记录！");
        }

        List<ChangeLogVO<DutyOplChangeLogVO>> changeLogVOs = new ArrayList<>();

        // 第一条记录
        DutyOperationLogVO logVO1 = logVOS.get(0);
        String createByName1 = logVO1.getCreateByName();
        Date createTime1 = logVO1.getCreateTime();
        String log1 = "1．" + DateUtil.formatDate(createTime1) + "，由 " + createByName1 + " 创建。 ";
        ChangeLogVO<DutyOplChangeLogVO> changeLogVO1 = new ChangeLogVO();
        changeLogVO1.setLog(log1);
        changeLogVOs.add(changeLogVO1);

        int index = 2;
        List<DutyOplChangeLogVO> clogs = dutyOplChangeLogMapper.listByLotNo(lotNo);
        if (CollUtil.isNotEmpty(clogs)) {
            Map<Date, List<DutyOplChangeLogVO>> clogMap = clogs.parallelStream()
                                                               .collect(Collectors.groupingBy(DutyOplChangeLogVO::getCreateTime, LinkedHashMap::new, Collectors.toList()));
            for (Map.Entry<Date, List<DutyOplChangeLogVO>> it : clogMap.entrySet()) {
                List<DutyOplChangeLogVO> vos = it.getValue();
                DutyOplChangeLogVO vo = vos.get(0);
                String log = index + "．" + DateUtil.formatDate(it.getKey()) + "，由 " + vo.getCreateByName() + " 变更。";
                ChangeLogVO<DutyOplChangeLogVO> changeLogVO = new ChangeLogVO();
                changeLogVO.setLog(log);
                changeLogVO.setDetails(vos);
                changeLogVOs.add(changeLogVO);
                index = index + 1;
            }
        }
        return changeLogVOs;
    }

    @Override
    public List<DutyOpVersionVO> listVersion(String lotNo) {
        return this.baseMapper.listVersion(lotNo);
    }

    @Override
    public DutyOperationLogVO detail(Integer id) {
        return this.baseMapper.detail(id);
    }

    @Override
    public List<DutyCalendarVO> getCalendar(String date) {
        List<DutyCalendarVO> vos = new ArrayList<>();
        if (StrUtil.isEmpty(date)) {
            date = DateUtil.today();
        }
        DateTime dateTime = DateUtil.parseDate(date);
        DateTime beginOfWeek = DateUtil.beginOfWeek(dateTime);
        DateTime endOfWeek = DateUtil.endOfWeek(dateTime);

        Map<String, DutyCalendarVO> voMap = this.baseMapper.listCalendar(beginOfWeek, endOfWeek);

        for (DateTime temp = beginOfWeek; temp.compareTo(endOfWeek) <= 0; temp.offset(DateField.DAY_OF_YEAR, 1)) {
            DutyCalendarVO vo = voMap.get(temp.toDateStr());
            // 获得指定日期是星期几，1表示周日，2表示周一
            int week = DateUtil.dayOfWeek(temp);
            if (vo == null) {
                vo = new DutyCalendarVO();
                vo.setDate(temp.toDateStr());
                vo.setB(null);
                vo.setZ(null);
                vo.setY(null);
            }
            int w = week - 1;
            vo.setWeek(w != 0 ? w : 7);
            vos.add(vo);
        }
        return vos;
    }

    @DSTransactional
    @Override
    public void updateInfo(DutyOperationLog log) {
        Integer id = log.getId();
        DutyOperationLog oldOperationLog = this.getById(id);
        String lotNo = oldOperationLog.getLotNo();
        ChangeLogUtil<DutyOplChangeLog> changeLogUtil = new ChangeLogUtil<DutyOplChangeLog>() {
            @Override
            protected DutyOplChangeLog initChangeLog() {
                DutyOplChangeLog changeLog = new DutyOplChangeLog();
                changeLog.setOplogId(id);
                changeLog.setOplogLotNo(lotNo);
                changeLog.setClassName(DutyOperationLog.class.getName());
                return changeLog;
            }
        };
        List<DutyOplChangeLog> dutyOplChangeLogs = changeLogUtil.compareFields(oldOperationLog, log);
        dutyOplChangeLogService.saveBatch(dutyOplChangeLogs);

        log.setId(null);
        log.setOpTime(oldOperationLog.getOpTime());
        log.setLotNo(lotNo);

        log.setHandoverBy(oldOperationLog.getHandoverBy());
        log.setHandoverDuty(oldOperationLog.getHandoverDuty());
        log.setHandoverTime(oldOperationLog.getHandoverTime());

        log.setTakeoverBy(oldOperationLog.getTakeoverBy());
        log.setTakeoverDuty(oldOperationLog.getTakeoverDuty());
        log.setTakeoverTime(oldOperationLog.getTakeoverTime());

        log.setOpType(oldOperationLog.getOpType());
        Integer version = oldOperationLog.getVersion();
        log.setVersion(version + 1);
        this.save(log);
    }
}
