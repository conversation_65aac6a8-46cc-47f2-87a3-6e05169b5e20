package com.zjwly.psoms.domain.vo;

import com.zjwly.psoms.domain.PeriodicPlan;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 定期计划VO
 *
 * <AUTHOR>
 * @date 2023/10/16 15:39
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class PeriodicPlanVO extends PeriodicPlan {
    /**
     * 创建人
     */
    private String createByName;
}
