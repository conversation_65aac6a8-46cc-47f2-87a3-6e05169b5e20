package com.zjwly.psoms.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zjwly.common.mybatis.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 值别安排记录
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName(value = "om_duty_record")
public class DutyRecord extends BaseEntity {
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 值别安排ID
     */
    @TableField(value = "duty_id")
    private Integer dutyId;

    /**
     * 生效时间
     */
    @TableField(value = "effective_time")
    private Date effectiveTime;

    /**
     * 失效时间
     */
    @TableField(value = "expiration_time")
    private Date expirationTime;

}
