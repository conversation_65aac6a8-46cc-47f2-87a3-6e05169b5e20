package com.zjwly.psoms.domain.vo;

import com.zjwly.psoms.domain.Attachment;
import com.zjwly.psoms.domain.EquipmentChange;
import lombok.Data;

import java.util.List;

/**
 * 设备异动VO
 *
 * <AUTHOR>
 * @date 2023/11/13 16:59
 */
@Data
public class EquipmentChangeVO extends EquipmentChange {
    /**
     * 异动内容：异动前附件
     */
    private List<Attachment> beforeAttachments;
    /**
     * 异动内容：异动后附件
     */
    private List<Attachment> afterAttachments;
    /**
     * 可能出现的问题及注意事项附件
     */
    private List<Attachment> qaReqAttachments;
    /**
     * 异动设备说明书等相关资料附件
     */
    private List<Attachment> manualAttachments;
    /**
     * 逻辑说明、保护定值/清册等资料附件
     */
    private List<Attachment> lePsInvAttachments;
    /**
     * 系统图、结构图等有关图纸资料附件
     */
    private List<Attachment> stSdAttachments;
    /**
     * 其他异动资料附件
     */
    private List<Attachment> otherAttachments;
    /**
     * 可能出现的问题及注意事项附件
     */
    private String qaRepAttachments;

    /**
     * 填报人名
     */
    private String reporterName;

    /**
     * 申请单流程OrderId
     */
    private String orderIdReq;


    /**
     * 设备流程的最终状态
     */
    private String finalStatus;

    /**
     * 申请单审批状态
     * UNCOMMITTED("未 提交"),
     * APPROVING("审批中"),
     * APPROVED("审批完成"),
     * REJECTED("已驳回"),
     * CANCELED("已销"),
     * OBSOLETE("已废除")
     */
    private String statusReq;

    /**
     * 申请单审批节点
     */
    private String displayNameReq;

    /**
     * 报告单流程OrderId
     */
    private String orderIdRep;

    /**
     * 报告单审批状态
     * UNCOMMITTED("未 提交"),
     * APPROVING("审批中"),
     * APPROVED("审批完成"),
     * REJECTED("已驳回"),
     * CANCELED("已销"),
     * OBSOLETE("已废除")
     */
    private String statusRep;

    /**
     * 报告单审批节点
     */
    private String displayNameRep;

    /**
     * 申请单第1组用户IdName列表
     */
    private List<IdNameTimeVO> group1ReqIdNames;
    /**
     * 申请单第2组用户IdName列表
     */
    private List<IdNameTimeVO> group2ReqIdNames;
    /**
     * 申请单第3组用户IdName列表
     */
    private List<IdNameTimeVO> group3ReqIdNames;
    /**
     * 申请单第4组用户IdName列表
     */
    private List<IdNameTimeVO> group4ReqIdNames;
    /**
     * 申请单第5组用户IdName列表
     */
    private List<IdNameTimeVO> group5ReqIdNames;
    /**
     * 申请单第6组用户IdName列表
     */
    private List<IdNameTimeVO> group6ReqIdNames;

    /**
     * 报告单第1组用户IdName列表
     */
    private List<IdNameTimeVO> group1RepIdNames;

    /**
     * 报告单第2组用户IdName列表
     */
    private List<IdNameTimeVO> group2RepIdNames;

    /**
     * 报告单第3组用户IdName列表
     */
    private List<IdNameTimeVO> group3RepIdNames;

    /**
     * 报告单第4组用户IdName列表
     */
    private List<IdNameTimeVO> group4RepIdNames;

    /**
     * 报告单第5组用户IdName列表
     */
    private List<IdNameTimeVO> group5RepIdNames;

}
