package com.zjwly.psoms.scheduler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.zjwly.psoms.domain.Defect;
import com.zjwly.psoms.enums.DefectStateEnum;
import com.zjwly.psoms.service.DefectService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 异常管理定时任务
 *
 * <AUTHOR>
 * @date 2023/11/06 15:29
 */
@Component
@RequiredArgsConstructor
public class DefectScheduledTask {
    private final DefectService defectService;

    /**
     * 1.状态为【缺陷下达】开始，计时72小时，未到【验收合格】，限定为缺陷超期，列表中整行标记（红色）底色
     * 2.延期审批通过后，不判定缺陷超期
     * 3.缺陷超期后，一直记录超期（红色字体展示），不影响其他操作
     *
     * @return void
     * <AUTHOR>
     * @date 2023/11/06 15:48
     */
    @DSTransactional
//    @Scheduled(cron = "0 */1 * * * ?")
    public void isOverdue() {
        DateTime dateTime = DateUtil.offsetHour(new Date(), -72);
        List<Defect> defects = defectService.lambdaQuery()
                                            .eq(Defect::getIsExtensionPass, 0)
                                            .ge(Defect::getProcessingStatus, DefectStateEnum.DEFECT_ISSUED)
                                            .lt(Defect::getProcessingStatus, DefectStateEnum.ACCEPTANCE)
                                            .lt(Defect::getReleaseTime, dateTime).list();
        if (CollUtil.isNotEmpty(defects)) {
            for (Defect defect : defects) {
                defect.setIsOverdue(1);
            }
            defectService.updateBatchById(defects);
        }
    }

}
