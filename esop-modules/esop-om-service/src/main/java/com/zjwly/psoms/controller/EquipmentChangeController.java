package com.zjwly.psoms.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.zjwly.common.base.Result;
import com.zjwly.psoms.domain.EquipmentChange;
import com.zjwly.psoms.domain.vo.EquipmentChangeVO;
import com.zjwly.psoms.service.EquipmentChangeService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 设备异动管理
 *
 * <AUTHOR>
 * @date 2023/11/10 17:22
 */
@RequiredArgsConstructor
@RequestMapping("/ds/om/equipmentChange")
@RestController
public class EquipmentChangeController {
    private final EquipmentChangeService equipmentChangeService;

    /**
     * 异动申请
     *
     * @param equipmentChange
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/11/10 17:26
     */
    @PostMapping("/saveReqForm")
    public Result saveReqForm(@RequestBody EquipmentChange equipmentChange) {
        Integer id = equipmentChangeService.saveReqForm(equipmentChange);
        return Result.ok(id);
    }

    /**
     * 填报申请
     *
     * @param equipmentChange
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/11/10 17:30
     */
    @PutMapping("/updateReqForm")
    public Result updateReqForm(@RequestBody EquipmentChange equipmentChange) {
        equipmentChangeService.updateReqForm(equipmentChange);
        return Result.ok();
    }

    /**
     * 删除申请单
     *
     * @param id 设备异动记录ID
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/11/13 16:50
     */
    @DeleteMapping("/deleteReqForm")
    public Result deleteReqForm(@RequestParam("id") Integer id) {
        equipmentChangeService.deleteReqForm(id);
        return Result.ok();
    }

    /**
     * 查看
     *
     * @param id
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/11/13 17:50
     * @response {@link Result<EquipmentChangeVO>}
     */
    @GetMapping("/getDetail")
    public Result getDetail(@RequestParam("id") Integer id) {
        EquipmentChangeVO vo = equipmentChangeService.getDetail(id);
        return Result.ok(vo);
    }

    /**
     * 生成报告单
     *
     * @param equipmentChange
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/11/14 10:08
     */
    @PutMapping("/saveRepForm")
    public Result saveRepForm(@RequestBody EquipmentChange equipmentChange) {
        equipmentChangeService.saveRepForm(equipmentChange);
        return Result.ok();
    }

    /**
     * 编辑报告单
     *
     * @param equipmentChange
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/11/14 10:12
     */
    @PutMapping("/updateRepForm")
    public Result updateRepForm(@RequestBody EquipmentChange equipmentChange) {
        equipmentChangeService.updateRepForm(equipmentChange);
        return Result.ok();
    }

    /**
     * 删除报告单
     *
     * @param id 设备异动记录ID
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/11/13 16:50
     */
    @DeleteMapping("/deleteRepForm")
    public Result deleteRepForm(@RequestParam("id") Integer id) {
        equipmentChangeService.deleteRepForm(id);
        return Result.ok();
    }

    /**
     * 分页查询列表
     *
     * @param reqNo         编号
     * @param equipmentName 设备名称
     * @param applicantUnit 申请单位
     * @param applicant     申请人
     * @param current       当前页
     * @param size          每页条数
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/11/14 16:43
     * @response {@link Result<com.baomidou.mybatisplus.extension.plugins.pagination.Page<EquipmentChangeVO>>}
     */
    @GetMapping("/list")
    public Result list(String reqNo, String equipmentName, String applicantUnit, String applicant,
                       @RequestParam(value = "current", required = false, defaultValue = "-1") Integer current,
                       @RequestParam(value = "size", required = false, defaultValue = "-1") Integer size) {
        IPage<EquipmentChangeVO> vo = equipmentChangeService.listInfo(reqNo, equipmentName, applicantUnit, applicant, current, size);

        List<EquipmentChangeVO> voList = vo.getRecords();

        List<Integer> equipmentIds = voList.stream().map(EquipmentChangeVO::getId).toList();

        if (!CollectionUtils.isEmpty(equipmentIds)){

            Map<Integer, Map<String, Object>> equipmentNameMap = equipmentChangeService.selectLatestStatusByBusinessIds(equipmentIds);

            voList.forEach(vo2 -> {
                Map<String, Object> equipmentNameMap1 = equipmentNameMap.get(vo2.getId());
                if (equipmentNameMap1 != null) {
                    vo2.setFinalStatus((String) equipmentNameMap1.get("status"));
                }
            });
            System.out.println(equipmentNameMap);
        }



        return Result.ok(vo);
    }

    /**
     * 保存用户组
     *
     * @param equipmentChange
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/11/17 15:10
     */
    @PutMapping("/saveGroup")
    public Result saveGroupe(@RequestBody EquipmentChange equipmentChange) {
        equipmentChangeService.updateById(equipmentChange);
        return Result.ok();
    }

    /**
     * 获取会签工序组
     *
     * @param orderId
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2024/01/15 17:56
     */
    @GetMapping("/getCountersignGroup")
    public Result getCountersignGroup(@RequestParam("orderId") String orderId) {
        Map<String, String> map = equipmentChangeService.getCountersignGroup(orderId);
        return Result.ok(map);
    }
}
