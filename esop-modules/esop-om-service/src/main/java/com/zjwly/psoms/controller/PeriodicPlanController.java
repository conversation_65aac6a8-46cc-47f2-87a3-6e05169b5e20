package com.zjwly.psoms.controller;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zjwly.common.base.Result;
import com.zjwly.psoms.domain.PeriodicPlan;
import com.zjwly.psoms.domain.vo.PeriodicPlanVO;
import com.zjwly.psoms.enums.PeriodicPlanStatusEnum;
import com.zjwly.psoms.service.PeriodicPlanService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 定期计划Controller
 *
 * <AUTHOR>
 * @date 2023/10/16 14:43
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/ds/om/periodPlan")
public class PeriodicPlanController {
    private final PeriodicPlanService periodicPlanService;

    /**
     * 创建
     *
     * @param periodicPlan
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/10/17 17:09
     */
    @PostMapping("/save")
    public Result save(@RequestBody PeriodicPlan periodicPlan) {
        periodicPlanService.saveInfo(periodicPlan);
        return Result.ok();
    }

    /**
     * 变更
     *
     * @param periodicPlan
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/10/17 17:09
     */
    @PutMapping("/update")
    public Result update(@RequestBody PeriodicPlan periodicPlan) {
        periodicPlanService.updateInfo(periodicPlan);
        return Result.ok();
    }

    /**
     * 删除
     *
     * @param null
     * @return
     * <AUTHOR>
     * @date 2023/10/17 17:10
     */
    @DeleteMapping("/delete/{lotNo}")
    public Result delete(@PathVariable("lotNo") String lotNo) {
        periodicPlanService.lambdaUpdate()
                           .eq(PeriodicPlan::getLotNo, lotNo)
                           .remove();
        return Result.ok();
    }

    /**
     * 下发
     *
     * @param lotNo 批号
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/10/17 17:10
     */
    @PutMapping("/issue/{lotNo}")
    public Result issue(@PathVariable("lotNo") String lotNo) {
        periodicPlanService.lambdaUpdate()
                           .eq(PeriodicPlan::getLotNo, lotNo)
                           .set(PeriodicPlan::getStatus, PeriodicPlanStatusEnum.ISSUED.getCode())
                           .update();
        return Result.ok();
    }

    @PutMapping("/cancelIssue/{lotNo}")
    public Result cancelIssue(@PathVariable("lotNo") String lotNo) {
        periodicPlanService.lambdaUpdate()
                           .eq(PeriodicPlan::getLotNo, lotNo)
                           .set(PeriodicPlan::getStatus, PeriodicPlanStatusEnum.NOT_ISSUED.getCode())
                           .update();
        return Result.ok();
    }

    /**
     * 查询列表
     *
     * @param startDate   开始月
     * @param endDate     结束月
     * @param cycleType   执行周期 0：班，1：日，2：周，3：月，4：季度，5：年
     * @param status      执行状态 0：未下发，1：已下发
     * @param taskContent 定期工作任务
     * @param current
     * @param size
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/10/16 15:36
     */
    @GetMapping("/list")
    public Result list(String startDate, String endDate, Integer cycleType, Integer status, String taskContent,
                       @RequestParam(value = "current", required = false, defaultValue = "-1") Integer current,
                       @RequestParam(value = "size", required = false, defaultValue = "-1") Integer size) {
        DateTime startTime = null;
        DateTime endTime = null;
        if (StrUtil.isNotBlank(startDate) && StrUtil.isNotBlank(endDate)) {
            startTime = DateUtil.parse(startDate, DatePattern.NORM_MONTH_PATTERN);
            endTime = DateUtil.parse(endDate, DatePattern.NORM_MONTH_PATTERN)
                              .offset(DateField.MONTH, 1);
        }
        IPage<PeriodicPlanVO> vos = periodicPlanService.listInfo(startTime, endTime, cycleType, status, taskContent, current, size);
        return Result.ok(vos);
    }

    /**
     * 变更记录
     *
     * @param lotNo   批号
     * @param current
     * @param size
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/10/16 17:36
     */
    @GetMapping("/listEditRecord")
    public Result listEditRecord(@RequestParam("lotNo") String lotNo,
                                 @RequestParam(value = "current", required = false, defaultValue = "-1") Integer current,
                                 @RequestParam(value = "size", required = false, defaultValue = "-1") Integer size) {
        IPage<PeriodicPlanVO> voPage = periodicPlanService.listEditRecord(lotNo, current, size);
        return Result.ok(voPage);
    }
}
