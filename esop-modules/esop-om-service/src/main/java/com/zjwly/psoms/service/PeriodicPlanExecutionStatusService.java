package com.zjwly.psoms.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zjwly.psoms.domain.PeriodicPlanExecutionStatus;
import com.zjwly.psoms.domain.vo.PeriodicPlanExecutionStatusVO;

public interface PeriodicPlanExecutionStatusService extends IService<PeriodicPlanExecutionStatus>{


    void updateInfo(PeriodicPlanExecutionStatus executionStatus);

    IPage<PeriodicPlanExecutionStatusVO> listInfo(Integer cycleType, String startDate, String endDate, Integer shiftType, Integer status, Long executorId, String taskContent, Integer current, Integer size);
}
