package com.zjwly.psoms.converter;


import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

/**
 * easyexcel审批类型转化器
 *
 * <AUTHOR>
 * @date 2023/10/26 11:24
 */
public class ApprovalTypeConverter implements Converter<String> {

    @Override
    public WriteCellData<?> convertToExcelData(String value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        String v;
        switch (value) {
            case "DZ_OMS_CREATE":
                v = "新建";
                break;
            case "DZ_OMS_UPDATE":
                v = "变更";
                break;
            case "DZ_OMS_PHASEOUT":
                v = "淘汰";
                break;
            default:
                v = "";
        }
        return new WriteCellData<>(v);
    }
}
