package com.zjwly.psoms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zjwly.common.vo.IdNameVO;
import com.zjwly.psoms.domain.EquipmentChange;
import com.zjwly.psoms.domain.vo.EquipmentChangeVO;
import com.zjwly.psoms.domain.vo.IdNameTimeVO;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface EquipmentChangeMapper extends BaseMapper<EquipmentChange> {
    EquipmentChangeVO getDetail(@Param("id") Integer id);

    /**
     * 分页查询列表
     *
     * @param reqNo         编号
     * @param equipmentName 设备名称
     * @param applicantUnit 申请单位
     * @param applicant     申请人
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.zjwly.psoms.domain.vo.EquipmentChangeVO>
     * <AUTHOR>
     * @date 2023/11/14 16:45
     */
    IPage<EquipmentChangeVO> listInfo(IPage<EquipmentChangeVO> page, @Param("reqNo") String reqNo,
                                      @Param("equipmentName") String equipmentName,
                                      @Param("applicantUnit") String applicantUnit, @Param("applicant") String applicant);

    /**
     * 获取待审核数量
     *
     * @param userId
     * @param startTime
     * @param endTime
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 2023/11/20 17:59
     */
    Integer getApprovingCount(@Param("userId") Long userId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 获取orderId最新一条任务操作人
     *
     * @param orderId
     * @return {@link IdNameTimeVO}
     * <AUTHOR>
     * @date 2023/11/24 13:44
     */
    IdNameTimeVO getLastTaskOperator(@Param("orderId") String orderId);

    /**
     * 根据orderId获取审批的业务ID和业务类型
     *
     * @param orderId
     * @return java.util.List<java.util.Map>
     * <AUTHOR>
     * @date 2024/01/15 17:11
     */
    Map<String, Object> getExtOperationByOrderId(@Param("orderId") String orderId);

    /**
     * 获取审批班组用户
     *
     * @param group
     * @return java.util.List<com.zjwly.common.vo.IdNameVO>
     * <AUTHOR>
     * @date 2024/01/16 10:16
     */
    List<IdNameVO> getGroup(@Param("group") String group);



    @Select({
        "<script>",
        "SELECT t1.business_id, t1.status ",
        "FROM ext_operation t1 ",
        "INNER JOIN (",
        "SELECT business_id, MAX(create_time) AS max_create_time ",
        "FROM ext_operation ",
        "WHERE business_type = 'PRODUCTION_EQUIPMENT_CHANGE_REPORT_FORM' and business_id IN ",
        "<foreach item='item' collection='businessIds' open='(' separator=',' close=')'>",
        "#{item}",
        "</foreach> ",
        "GROUP BY business_id",
        ") t2 ",
        "ON t1.business_id = t2.business_id AND t1.create_time = t2.max_create_time",
        "</script>"
    })
    @MapKey("business_id")
    Map<Integer,Map<String, Object>> selectLatestStatusByBusinessIds(@Param("businessIds") List<Integer> businessIds);
}
