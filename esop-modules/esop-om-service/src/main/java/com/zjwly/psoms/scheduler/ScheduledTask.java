package com.zjwly.psoms.scheduler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.zjwly.psoms.domain.PeriodicPlan;
import com.zjwly.psoms.domain.PeriodicPlanExecutionStatus;
import com.zjwly.psoms.enums.PeriodicPlanCycleTypeEnum;
import com.zjwly.psoms.enums.PeriodicPlanStatusEnum;
import com.zjwly.psoms.enums.ShiftTypeEnum;
import com.zjwly.psoms.mapper.PeriodicPlanExecutionStatusMapper;
import com.zjwly.psoms.mapper.PeriodicPlanMapper;
import com.zjwly.psoms.service.PeriodicPlanExecutionStatusService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Data
@Component
@Slf4j
@RequiredArgsConstructor
public class ScheduledTask {
    private final PeriodicPlanMapper periodicPlanMapper;
    private final PeriodicPlanExecutionStatusService executionStatusService;
    private final PeriodicPlanExecutionStatusMapper executionStatusMapper;
    private String date;
    private Integer status;

    @DSTransactional
    @Scheduled(cron = "0 0 0 * * ?")
    public void createExecutionStaus() {
        log.info("----------------定时任务：创建任务执行情况！----------------");
        String today = DateUtil.today();
        if (StrUtil.isNotBlank(date)) {
            today = date;
        }
        DateTime dateTime = DateUtil.parseDate(today);
        if (status == null) {
            status = PeriodicPlanStatusEnum.ISSUED.getCode();
        }
        Date now = new Date();
        List<PeriodicPlan> periodicPlans = periodicPlanMapper.listSchedulerData(today, today, status);
        if (CollUtil.isNotEmpty(periodicPlans)) {
            List<PeriodicPlanExecutionStatus> list = new ArrayList<>();
            for (PeriodicPlan periodicPlan : periodicPlans) {
                Integer ppId = periodicPlan.getId();
                Date start = periodicPlan.getStartDate();
                Date end = periodicPlan.getEndDate();
                if (Objects.equals(PeriodicPlanCycleTypeEnum.SHIFT.getCode(), periodicPlan.getCycleType())) {
                    // 班，生成白、中、夜班三条数据
                    Long count = executionStatusService.lambdaQuery()
                            .eq(PeriodicPlanExecutionStatus::getPpId, ppId)
                            .eq(PeriodicPlanExecutionStatus::getStartDate, DateUtil.formatDate(dateTime))
                            .eq(PeriodicPlanExecutionStatus::getEndDate, DateUtil.formatDate(dateTime))
                            .count();
                    if (count == 0) {
                        PeriodicPlanExecutionStatus executionStatus1 = fillExecutionStatusProperties(periodicPlan, dateTime, dateTime, now);
                        executionStatus1.setShiftType(ShiftTypeEnum.DAY_SHIFT.getCode());
                        list.add(executionStatus1);

                        PeriodicPlanExecutionStatus executionStatus2 = fillExecutionStatusProperties(periodicPlan, dateTime, dateTime, now);
                        executionStatus2.setShiftType(ShiftTypeEnum.MIDDLE_SHIFT.getCode());
                        list.add(executionStatus2);

                        PeriodicPlanExecutionStatus executionStatus3 = fillExecutionStatusProperties(periodicPlan, dateTime, dateTime, now);
                        executionStatus3.setShiftType(ShiftTypeEnum.NIGHT_SHIFT.getCode());
                        list.add(executionStatus3);
                    }
                } else if (Objects.equals(PeriodicPlanCycleTypeEnum.DAY.getCode(), periodicPlan.getCycleType())) {
                    // 日
                    Long count = executionStatusService.lambdaQuery()
                            .eq(PeriodicPlanExecutionStatus::getPpId, ppId)
                            .eq(PeriodicPlanExecutionStatus::getStartDate, DateUtil.formatDate(dateTime))
                            .eq(PeriodicPlanExecutionStatus::getEndDate, DateUtil.formatDate(dateTime))
                            .count();
                    if (count == 0) {
                        PeriodicPlanExecutionStatus executionStatus = fillExecutionStatusProperties(periodicPlan, dateTime, dateTime, now);
                        list.add(executionStatus);
                    }
                } else if (Objects.equals(PeriodicPlanCycleTypeEnum.WEEK.getCode(), periodicPlan.getCycleType())) {
                    // 周
                    // 将开始结束，划分出多个周的间隔
                    // 例如：开始2023-10-07，结束2023-10-19 ——》一共三周，分别10.07~10.08，10.09~10.15，10.16~10.19
                    // 计算方法：1、得出 2023-10-07 所在这周的周日（10.08），小于 10.19，则第一周范围是 10.07 ~ 10.08；
                    // 2、2023-10-07 加 7 天得 10.14 小于 10.19，得出 10.14 所在这周的周一周日日期（10.09 ~ 10.15），则第二周范围是 10.09 ~ 10.15
                    // 3、2023-10-14 加 7 天得 10.21 大于 10.19，得出 10.19 所在这周的周一（10.16），则第三周范围是 10.16 ~ 10.19
                    // 例2：开始时间2023-10-07，结束时间2023-10-07，所在周周一周日（10.02~10.08），大于 10.07，则一周范围 10.07 ~ 10.07
                    long between = DateUtil.weekOfYear(end) - DateUtil.weekOfYear(start);
                    DateTime temp = new DateTime(start);
                    for (long l = 0; l < between + 1; l++) {
                        DateTime beginOfWeek = DateUtil.beginOfWeek(temp);
                        DateTime endOfWeek = DateUtil.endOfWeek(temp);
                        if (between == 0) {
                            // 当开始结束在一周内，即不跨周时，且未生成记录
                            Long count = executionStatusService.lambdaQuery()
                                    .eq(PeriodicPlanExecutionStatus::getPpId, ppId)
                                    .eq(PeriodicPlanExecutionStatus::getStartDate, DateUtil.formatDate(start))
                                    .eq(PeriodicPlanExecutionStatus::getEndDate, DateUtil.formatDate(end))
                                    .count();
                            if (count == 0) {
                                PeriodicPlanExecutionStatus executionStatus = fillExecutionStatusProperties(periodicPlan, start, end, now);
                                int weekNo = DateUtil.weekOfMonth(start);
                                executionStatus.setWeekNo(weekNo);
                                list.add(executionStatus);
                            }
                        } else if (l == 0) {
                            // 当开始结束日期范围存在跨周时，第一周范围是：开始时间~周日日期
                            Long count = executionStatusService.lambdaQuery()
                                    .eq(PeriodicPlanExecutionStatus::getPpId, ppId)
                                    .eq(PeriodicPlanExecutionStatus::getStartDate, DateUtil.formatDate(start))
                                    .eq(PeriodicPlanExecutionStatus::getEndDate, DateUtil.formatDate(endOfWeek))
                                    .count();
                            if (count == 0) {
                                PeriodicPlanExecutionStatus executionStatus = fillExecutionStatusProperties(periodicPlan, start, endOfWeek, now);
                                int weekNo = DateUtil.weekOfMonth(start);
                                executionStatus.setWeekNo(weekNo);
                                list.add(executionStatus);
                            }
                        } else if (end.compareTo(temp) <= 0) {
                            // 当开始结束日期范围存在跨周时，最后一周范围是：周一日期~结束日期
                            Long count = executionStatusService.lambdaQuery()
                                    .eq(PeriodicPlanExecutionStatus::getPpId, ppId)
                                    .eq(PeriodicPlanExecutionStatus::getStartDate, DateUtil.formatDate(beginOfWeek))
                                    .eq(PeriodicPlanExecutionStatus::getEndDate, DateUtil.formatDate(end))
                                    .count();
                            if (count == 0) {
                                PeriodicPlanExecutionStatus executionStatus = fillExecutionStatusProperties(periodicPlan, beginOfWeek, end, now);
                                int weekNo = DateUtil.weekOfMonth(beginOfWeek);
                                executionStatus.setWeekNo(weekNo);
                                list.add(executionStatus);
                            }
                        } else {
                            // 当开始结束日期范围存在跨周时，中间的一周范围是：周一日期~周日日期
                            Long count = executionStatusService.lambdaQuery()
                                    .eq(PeriodicPlanExecutionStatus::getPpId, ppId)
                                    .eq(PeriodicPlanExecutionStatus::getStartDate, DateUtil.formatDate(beginOfWeek))
                                    .eq(PeriodicPlanExecutionStatus::getEndDate, DateUtil.formatDate(endOfWeek))
                                    .count();
                            if (count == 0) {
                                PeriodicPlanExecutionStatus executionStatus = fillExecutionStatusProperties(periodicPlan, beginOfWeek, endOfWeek, now);
                                int weekNo = DateUtil.weekOfMonth(beginOfWeek);
                                executionStatus.setWeekNo(weekNo);
                                list.add(executionStatus);
                            }
                        }

                        temp.offset(DateField.DAY_OF_YEAR, 7);
                    }
                } else if (Objects.equals(PeriodicPlanCycleTypeEnum.MONTH.getCode(), periodicPlan.getCycleType())) {
                    // 月
                    long between = DateUtil.betweenMonth(start, end, true);
                    DateTime temp = new DateTime(start);
                    for (long l = 0; l < between + 1; l++) {
                        DateTime beginOfMonth = DateUtil.beginOfMonth(temp);
                        DateTime endOfMonth = DateUtil.endOfMonth(temp);
                        if (between == 0) {
                            // 开始结束在一月内，且未生成记录
                            Long count = executionStatusService.lambdaQuery()
                                    .eq(PeriodicPlanExecutionStatus::getPpId, ppId)
                                    .eq(PeriodicPlanExecutionStatus::getStartDate, DateUtil.formatDate(start))
                                    .eq(PeriodicPlanExecutionStatus::getEndDate, DateUtil.formatDate(end))
                                    .count();
                            if (count == 0) {
                                PeriodicPlanExecutionStatus executionStatus = fillExecutionStatusProperties(periodicPlan, start, end, now);
                                list.add(executionStatus);
                            }
                        } else if (l == 0) {
                            // 当开始结束日期范围存在跨月时，第一个月范围是：开始日期~结束日期
                            Long count = executionStatusService.lambdaQuery()
                                    .eq(PeriodicPlanExecutionStatus::getPpId, ppId)
                                    .eq(PeriodicPlanExecutionStatus::getStartDate, DateUtil.formatDate(start))
                                    .eq(PeriodicPlanExecutionStatus::getEndDate, DateUtil.formatDate(endOfMonth))
                                    .count();
                            if (count == 0) {
                                PeriodicPlanExecutionStatus executionStatus = fillExecutionStatusProperties(periodicPlan, start, endOfMonth, now);
                                list.add(executionStatus);
                            }
                        } else if (end.compareTo(endOfMonth) <= 0) {
                            // 当开始结束日期范围存在跨月时，最后一月范围是：月一号日期~结束日期
                            Long count = executionStatusService.lambdaQuery()
                                    .eq(PeriodicPlanExecutionStatus::getPpId, ppId)
                                    .eq(PeriodicPlanExecutionStatus::getStartDate, DateUtil.formatDate(beginOfMonth))
                                    .eq(PeriodicPlanExecutionStatus::getEndDate, DateUtil.formatDate(end))
                                    .count();
                            if (count == 0) {
                                PeriodicPlanExecutionStatus executionStatus = fillExecutionStatusProperties(periodicPlan, beginOfMonth, end, now);
                                list.add(executionStatus);
                            }
                        } else {
                            // 当开始结束日期范围存在跨月时，中间的一月范围是：月一号日期~月最后一天日期
                            Long count = executionStatusService.lambdaQuery()
                                    .eq(PeriodicPlanExecutionStatus::getPpId, ppId)
                                    .eq(PeriodicPlanExecutionStatus::getStartDate, DateUtil.formatDate(beginOfMonth))
                                    .eq(PeriodicPlanExecutionStatus::getEndDate, DateUtil.formatDate(endOfMonth))
                                    .count();
                            if (count == 0) {
                                PeriodicPlanExecutionStatus executionStatus = fillExecutionStatusProperties(periodicPlan, beginOfMonth, endOfMonth, now);
                                list.add(executionStatus);
                            }
                        }

                        temp.offset(DateField.MONTH, 1);
                    }
                } else if (Objects.equals(PeriodicPlanCycleTypeEnum.QUARTER.getCode(), periodicPlan.getCycleType())) {
                    // 季度
                    // 当前日期所属季度的开始 ~ 结束时间
                    DateTime beginOfQuarter = DateUtil.beginOfQuarter(now);
                    DateTime endOfQuarter = DateUtil.endOfQuarter(now);
                    Integer count = executionStatusMapper.countByPpIdAndDate(ppId, DateUtil.formatDate(beginOfQuarter), DateUtil.formatDate(endOfQuarter));
                    if (count == 0) {
                        PeriodicPlanExecutionStatus executionStatus = fillExecutionStatusProperties(periodicPlan, beginOfQuarter, endOfQuarter, now);
                        list.add(executionStatus);
                    }
                } else if (Objects.equals(PeriodicPlanCycleTypeEnum.YEAR.getCode(), periodicPlan.getCycleType())) {
                    // 年度 同季度
                    // 当前日期所属年的开始 ~ 结束时间
                    DateTime beginOfYear = DateUtil.beginOfYear(now);
                    DateTime endOfYear = DateUtil.endOfYear(now);
                    Integer count = executionStatusMapper.countByPpIdAndDate(ppId, DateUtil.formatDate(beginOfYear), DateUtil.formatDate(endOfYear));
                    if (count == 0) {
                        PeriodicPlanExecutionStatus executionStatus = fillExecutionStatusProperties(periodicPlan, beginOfYear, endOfYear, now);
                        list.add(executionStatus);
                    }
                }
            }
            executionStatusService.saveBatch(list);
        }
    }

    private PeriodicPlanExecutionStatus fillExecutionStatusProperties(PeriodicPlan periodicPlan, Date startDate,
                                                                      Date endDate, Date now) {
        PeriodicPlanExecutionStatus executionStatus = new PeriodicPlanExecutionStatus();
        executionStatus.setPpId(periodicPlan.getId());
        executionStatus.setCycleType(periodicPlan.getCycleType());
        executionStatus.setTaskContent(periodicPlan.getTaskContent());

        executionStatus.setStartDate(DateUtil.parseDate(DateUtil.formatDate(startDate)));
        executionStatus.setEndDate(DateUtil.parseDate(DateUtil.formatDate(endDate)));

        executionStatus.setCreateBy(1L);
        executionStatus.setCreateTime(now);
        executionStatus.setUpdateBy(1L);
        executionStatus.setUpdateTime(now);
        return executionStatus;
    }

}
