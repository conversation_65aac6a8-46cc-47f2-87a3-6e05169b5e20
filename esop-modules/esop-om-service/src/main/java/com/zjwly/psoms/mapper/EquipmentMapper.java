package com.zjwly.psoms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zjwly.psoms.domain.Equipment;
import com.zjwly.psoms.domain.vo.EquipmentVO;
import com.zjwly.psoms.domain.vo.EquipmentVersionVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface EquipmentMapper extends BaseMapper<Equipment> {
    /**
     * 操作区列表
     *
     * @param name           设备名称
     * @param no             设备编号
     * @param type           类别 DZ_OMS_CREATE,DZ_OMS_UPDATE,DZ_OMS_PHASEOUT
     * @param approvalStatus 审批状态 APPROVING("审批中"),REJECTED("已驳回"),CANCELED("已撤销"),
     * @return
     * <AUTHOR>
     * @date 2023/10/25 14:57
     */
    IPage<EquipmentVO> listOpArea(IPage<EquipmentVO> page, @Param("name") String name, @Param("no") String no, @Param("type") String type, @Param("approvalStatus") String approvalStatus);

    /**
     * 设备台账列表
     *
     * @param page
     * @param name 设备名称
     * @param no   设备编号
     * @param type 类别 DZ_OMS_CREATE,DZ_OMS_UPDATE,DZ_OMS_PHASEOUT
     * @return
     * <AUTHOR>
     * @date 2023/10/25 21:57
     */
    IPage<EquipmentVO> listInfo(IPage<EquipmentVO> page, @Param("name") String name, @Param("no") String no, @Param("type") String type);

    /**
     * 版本列表
     *
     * @param lotNo
     * @return java.util.List<com.zjwly.psoms.domain.vo.EquipmentVersionVO>
     * <AUTHOR>
     * @date 2023/10/25 22:15
     */
    List<EquipmentVersionVO> listVersion(@Param("lotNo") String lotNo);

    /**
     * 详情
     *
     * @param id
     * @return com.zjwly.psoms.domain.vo.EquipmentVO
     * <AUTHOR>
     * @date 2023/10/26 16:09
     */
    EquipmentVO getDetail(@Param("id") Integer id, @Param("status") String status, @Param("type") String type);

    /**
     * 获取非淘汰的审批通过的列表
     *
     * @param name
     * @param no
     * @return java.util.List<com.zjwly.psoms.domain.vo.EquipmentVO>
     * <AUTHOR>
     * @date 2023/10/27 10:47
     */
    List<EquipmentVO> listApprovedInfo(@Param("name") String name, @Param("no") String no);

    /**
     * listApprovedIds
     *
     * @param lotNo
     * @return
     */
    List<Integer> listApprovedIds(@Param("lotNo") String lotNo);

    /**
     * 获取待审核数量
     * <AUTHOR>
     * @date 2023/11/20 15:31
     * @param userId
     * @param startTime
     * @param endTime
     * @return java.lang.Integer
     */
    Integer getApprovingCount(@Param("userId") Long userId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);
}