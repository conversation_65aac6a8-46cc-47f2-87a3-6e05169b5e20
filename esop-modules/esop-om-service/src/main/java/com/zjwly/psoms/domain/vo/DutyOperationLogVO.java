package com.zjwly.psoms.domain.vo;

import com.zjwly.psoms.domain.DutyOperationLog;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 运行日志VO
 *
 * <AUTHOR>
 * @date 2023/10/13 11:38
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class DutyOperationLogVO extends DutyOperationLog {
    /**
     * 交班人
     */
    private String handoverName;

    /**
     * 接班人
     */
    private String takeoverName;

    /**
     * 创建人
     */
    private String createByName;
}
