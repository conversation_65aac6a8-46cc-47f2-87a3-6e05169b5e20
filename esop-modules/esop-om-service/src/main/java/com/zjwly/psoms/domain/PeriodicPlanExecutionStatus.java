package com.zjwly.psoms.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 定期工作执行情况
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "om_periodic_plan_execution_status")
public class PeriodicPlanExecutionStatus {
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 关联的定期计划ID
     */
    private Integer ppId;

    /**
     * 计划日期开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @TableField(value = "start_date")
    private Date startDate;

    /**
     * 计划日期结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @TableField(value = "end_date")
    private Date endDate;

    /**
     * 第几周
     */
    @TableField(value = "week_no")
    private Integer weekNo;

    /**
     * 周期类型 0：班，1：日，2：周，3：月
     */
    @TableField(value = "cycle_type")
    private Integer cycleType;

    /**
     * 班类型 0：白班，1：中班，2：夜班
     */
    private Integer shiftType;

    /**
     * 任务内容
     */
    @TableField(value = "task_content")
    private String taskContent;

    /**
     * 填报状态 0：未完成，1：已完成
     */
    @TableField(value = "`status`")
    private Integer status;

    /**
     * 执行情况
     */
    @TableField(value = "execution_content")
    private String executionContent;

    /**
     * 完成时间
     */
    @TableField(value = "finish_time")
    private Date finishTime;

    /**
     * 执行人
     */
    @TableField(value = "executor_id")
    private Long executorId;

    /**
     * 填报时间
     */
    private Date fillTime;

    @TableField(value ="create_by")
    private Long createBy;

    @TableField(value ="create_time")
    private Date createTime;

    @TableField(value ="update_by")
    private Long updateBy;

    @TableField(value ="update_time")
    private Date updateTime;
}
