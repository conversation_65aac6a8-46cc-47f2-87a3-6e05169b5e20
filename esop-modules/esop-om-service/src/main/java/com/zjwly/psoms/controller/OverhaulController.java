package com.zjwly.psoms.controller;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zjwly.common.base.Result;
import com.zjwly.common.easyexcel.CustomCellWriteWeightConfig;
import com.zjwly.psoms.domain.Overhaul;
import com.zjwly.psoms.service.OverhaulService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

/**
 * 检修设备台账
 *
 * <AUTHOR>
 * @date 2023/11/08 15:06
 */
@RequiredArgsConstructor
@RequestMapping("/ds/om/overhaul")
@RestController
public class OverhaulController {
    private final OverhaulService overhaulService;

    /**
     * 查询列表
     *
     * @param name    设备名称
     * @param type    检修类别
     * @param current 当前页
     * @param size    每页条数
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/11/08 15:22
     */
    @GetMapping("/list")
    public Result list(String name, Integer type,
                       @RequestParam(value = "current", required = false, defaultValue = "-1") Long current,
                       @RequestParam(value = "size", required = false, defaultValue = "-1") Long size) {

        IPage<Overhaul> overhaulPage = overhaulService.listInfo(name, type, current, size);
        return Result.ok(overhaulPage);
    }

    /**
     * 新增
     *
     * @param overhaul
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/11/08 15:13
     */
    @PostMapping("/save")
    public Result save(@RequestBody Overhaul overhaul) {
        overhaulService.save(overhaul);
        return Result.ok();
    }

    /**
     * 修改
     *
     * @param overhaul
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/11/08 15:16
     */
    @PutMapping("/update")
    public Result update(@RequestBody Overhaul overhaul) {
        overhaulService.updateById(overhaul);
        return Result.ok();
    }

    /**
     * 删除
     *
     * @param id
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/11/08 15:17
     */
    @DeleteMapping("/delete")
    public Result delete(@RequestParam("id") Integer id) {
        overhaulService.removeById(id);
        return Result.ok();
    }

    /**
     * 导出
     *
     * @param name     设备名称
     * @param type     检修类别
     * @param current  当前页
     * @param size     每页条数
     * @param response
     * @return void
     * <AUTHOR>
     * @date 2023/11/08 15:52
     */
    @GetMapping("/export")
    public void export(String name, Integer type,
                       @RequestParam(value = "current", required = false, defaultValue = "-1") Long current,
                       @RequestParam(value = "size", required = false, defaultValue = "-1") Long size,
                       HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("检修台账", "UTF-8")
            .replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        IPage<Overhaul> overhaul = overhaulService.listInfo(name, type, current, size);
        List<Overhaul> records = overhaul.getRecords();
        EasyExcel.write(response.getOutputStream(), Overhaul.class)
            .registerWriteHandler(new CustomCellWriteWeightConfig())
            .sheet()
            .doWrite(records);
    }

}
