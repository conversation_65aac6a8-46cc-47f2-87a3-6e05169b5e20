package com.zjwly.psoms.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zjwly.psoms.domain.Attachment;
import com.zjwly.psoms.domain.Equipment;
import com.zjwly.psoms.domain.vo.EquipmentVO;
import com.zjwly.psoms.domain.vo.EquipmentVersionVO;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

public interface EquipmentService extends IService<Equipment> {


    void updateInfo(Equipment equipment);

    /**
     * 操作区列表
     *
     * @param name           设备名称
     * @param no             设备编号
     * @param type           类别
     * @param approvalStatus 审批状态 APPROVING("审批中"),REJECTED("已驳回"),CANCELED("已撤销"),
     * @param current
     * @param size
     * @return
     * @date 2023/10/25 14:54
     */
    IPage<EquipmentVO> listOpArea(String name, String no, String type, String approvalStatus, Long current, Long size);

    /**
     * 设备台账
     *
     * @param name    设备名称
     * @param no      设备编号
     * @param type    类别
     * @param current
     * @param size
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.zjwly.psoms.domain.vo.EquipmentVO>
     * <AUTHOR>
     * @date 2023/10/25 22:06
     */
    IPage<EquipmentVO> listInfo(String name, String no, String type, Long current, Long size);

    /**
     * 版本列表
     *
     * @param lotNo
     * @return java.util.List<com.zjwly.psoms.domain.vo.EquipmentVersionVO>
     * <AUTHOR>
     * @date 2023/10/25 22:14
     */
    List<EquipmentVersionVO> listVersion(String lotNo);

    void delete(List<Integer> ids);

    /**
     * 设备详情
     *
     * @param id
     * @param status 审批状态 APPROVING("审批中"),REJECTED("已驳回"),CANCELED("已撤销"),APPROVED("审批通过")
     * @return com.zjwly.psoms.domain.vo.EquipmentVO
     * <AUTHOR>
     * @date 2023/10/26 16:07
     */
    EquipmentVO getDetail(Integer id, String status, String type);

    /**
     * 批量上传文件
     *
     * @param files
     * @return void
     * <AUTHOR>
     * @date 2023/10/26 17:07
     */
    List<Attachment> upload(List<MultipartFile> files) throws IOException;

    /**
     * 批量保存
     * <AUTHOR>
     * @date 2023/11/02 10:16
     * @param cachedDataList
     * @return void
     */
    void saveInfoBatch(List<Equipment> cachedDataList);

    /**
     * 检查名称
     *
     * @param name
     * @return com.zjwly.common.base.Result
     * <AUTHOR>
     * @date 2023/10/26 14:48
     */
    void checkName(String name);

    /**
     * 获取当前设备审批通过的业务ID
     * <AUTHOR>
     * @date 2023/11/03 14:45
     * @param lotNo 批号
     * @return java.util.List<java.lang.Integer>
     */
    List<Integer> listApprovedIds(String lotNo);
}
