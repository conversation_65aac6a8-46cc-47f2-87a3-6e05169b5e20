package com.zjwly.psoms.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.zjwly.psoms.enums.DefectStateEnum;

/**
 * easyexcel 缺陷状态转换器
 *
 * <AUTHOR>
 * @date 2023/11/08 11:11
 */
public class DefectStateConverter implements Converter<DefectStateEnum> {
    @Override
    public Class<?> supportJavaTypeKey() {
        return DefectStateEnum.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public DefectStateEnum convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        String value = cellData.getStringValue();
        return DefectStateEnum.getByValue(value);
    }

    @Override
    public WriteCellData<?> convertToExcelData(DefectStateEnum value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        String displayName = value.getDisplayName();
        return new WriteCellData<>(displayName);
    }

}
