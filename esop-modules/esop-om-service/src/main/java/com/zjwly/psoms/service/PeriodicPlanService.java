package com.zjwly.psoms.service;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zjwly.psoms.domain.PeriodicPlan;
import com.zjwly.psoms.domain.vo.PeriodicPlanVO;

public interface PeriodicPlanService extends IService<PeriodicPlan> {


    void saveInfo(PeriodicPlan periodicPlan);

    void updateInfo(PeriodicPlan periodicPlan);

    IPage<PeriodicPlanVO> listInfo(DateTime startTime, DateTime endTime, Integer cycleType, Integer status, String taskContent, Integer current, Integer size);

    /**
     * 变更记录
     *
     * @param lotNo   批号
     * @param current
     * @param size
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.zjwly.psoms.domain.vo.PeriodicPlanVO>
     * <AUTHOR>
     * @date 2023/10/16 17:37
     */
    IPage<PeriodicPlanVO> listEditRecord(String lotNo, Integer current, Integer size);
}
