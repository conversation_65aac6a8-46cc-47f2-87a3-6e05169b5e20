package com.zjwly.psoms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zjwly.psoms.domain.DutyRecord;
import com.zjwly.psoms.domain.vo.DutyRecordVO;
import org.apache.ibatis.annotations.Param;

public interface DutyRecordMapper extends BaseMapper<DutyRecord> {
    IPage<DutyRecordVO> selectPpageInfo(IPage<DutyRecordVO> page, @Param("startTime") String startTime, @Param("endTime") String endTime);
}