package com.zjwly.psoms.converter;

import com.alibaba.excel.converters.integer.IntegerStringConverter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

/**
 * easyexcel 缺陷类别转换器
 *
 * <AUTHOR>
 * @date 2023/11/08 11:11
 */
public class DefectTypeConverter extends IntegerStringConverter {

    @Override
    public WriteCellData<?> convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        String v;
        switch (value.intValue()) {
            case 1:
                v = "一类";
                break;
            case 2:
                v = "二类";
                break;
            case 3:
                v = "三类";
                break;
            default:
                v = "";
        }
        return new WriteCellData<>(v);
    }
}
