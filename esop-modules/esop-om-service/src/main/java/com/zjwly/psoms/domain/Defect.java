package com.zjwly.psoms.domain;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zjwly.common.annotation.ChangeLog;
import com.zjwly.common.mybatis.core.domain.BaseEntity;
import com.zjwly.psoms.converter.DefectStateConverter;
import com.zjwly.psoms.converter.DefectTypeConverter;
import com.zjwly.psoms.enums.DefectStateEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 缺陷
 */
@ExcelIgnoreUnannotated
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "om_defect")
public class Defect extends BaseEntity {
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 工单编号
     */
    @ExcelProperty("缺陷工单编号")
    @TableField(value = "work_order_no")
    private String workOrderNo;

    /**
     * 序号
     */
    @TableField(value = "serial_no")
    private Integer serialNo;

    /**
     * 设备ID
     */
    @TableField(value = "equipment_id")
    private Integer equipmentId;

    /**
     * 设备批号
     */
    private String equipmentLotNo;

    /**
     * 设备名称
     */
    @ExcelProperty("设备名称")
    private String equipmentName;

    /**
     * 设备编号
     */
    @ExcelProperty("设备编号")
    private String equipmentNo;

    /**
     * 缺陷类别 1：一类，2：二类，3：三类
     */
    @ExcelProperty(value = "缺陷类别", converter = DefectTypeConverter.class)
    @ChangeLog
    @TableField(value = "defect_type")
    private Integer defectType;

    /**
     * 缺陷描述
     */
    @ExcelProperty("缺陷描述")
    @ChangeLog
    @TableField(value = "description")
    private String description;

    /**
     * 缺陷下达时间
     */
    @ExcelProperty("缺陷下达时间")
    @TableField(value = "release_time")
    private Date releaseTime;

    /**
     * 缺陷处理状态 DRAFT：草稿，DEFECT_ENTRY：缺陷录入，DEFECT_ISSUED：缺陷下达，WORK_ASSIGNMENT：工作安排，WORK_COMPLETED：工作完成，ACCEPTANCE：验收合格，DEFECT_CLOSED：缺陷关闭，WORK_ORDER_CANCELED_REQUEST：工单取消申请，EXTENSION_REQUEST：延期申请，WORK_ORDER_CANCELED：工单取消，EXTENSION_APPROVED：延期申请通过
     */
    @ExcelProperty(value = "缺陷处理状态", converter = DefectStateConverter.class)
    @TableField(value = "processing_status")
    private DefectStateEnum processingStatus;

    /**
     * 上一步处理状态
     */
    @TableField(value = "pre_processing_status")
    private DefectStateEnum preProcessingStatus;

    /**
     * 缺陷关闭时间
     */
    @ExcelProperty("缺陷关闭时间")
    @TableField(value = "close_time")
    private Date closeTime;

    /**
     * 备注
     */
    @ChangeLog
    private String remark;

    /**
     * 工作安排
     */
    @ChangeLog
    private String workContent;

    /**
     * 延期时间
     */
    @ChangeLog
    private Date extensionTime;

    /**
     * 延期原因
     */
    @ChangeLog
    private String extensionReson;

    /**
     * 是否延期审核通过 0：否，1：是
     */
    private Integer isExtensionPass;

    /**
     * 是否缺陷超期 0：未超期，1：超期
     */
    private Integer isOverdue;

    /**
     * 逻辑删除 0：未删除，1：删除
     */
    @TableField(value = "deleted")
    private Integer deleted;

}
