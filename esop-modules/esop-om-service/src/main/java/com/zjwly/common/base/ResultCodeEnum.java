package com.zjwly.common.base;

import lombok.Getter;

/**
 * 统一返回结果状态信息类
 *
 */
@Getter
public enum ResultCodeEnum {

    SUCCESS(200,"成功"),
    FAIL(201, "失败"),
    UNAUTHORIZED(401,"未找到登录信息"),
    SERVICE_ERROR(2012, "服务异常"),
    PERMISSION(4000,"请求参数有误"),
    FILE_EXIST(4001,"文件已经存在")

    ;

    private Integer code;

    private String message;

    private ResultCodeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
}
