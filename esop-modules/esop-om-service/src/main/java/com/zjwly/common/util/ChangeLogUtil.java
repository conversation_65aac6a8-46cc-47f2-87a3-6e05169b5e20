package com.zjwly.common.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.zjwly.common.annotation.ChangeLog;
import com.zjwly.common.exception.ServiceException;
import com.zjwly.common.satoken.utils.LoginHelper;

import java.lang.reflect.Field;
import java.util.*;

/**
 * 工具类：比较两个对象并获取其中不相等的字段
 * 需要配合@ChangeLog注解进行使用
 * 只有使用@ChangeLog注解标注的属性才会生成ChangeLog
 */
public abstract class ChangeLogUtil<T extends BaseChangeLog> {

    /**
     * 比较两个对象属性o1和o2是否相等 相等返回true(子类根据实际情况重写对比方法)
     *
     * @param o1
     * @param o2
     * @return
     */
    public boolean isEquals(Object o1, Object o2) {
        if (StrUtil.isEmptyIfStr(o1) && StrUtil.isEmptyIfStr(o2)) {
            return true;
        }
        return ObjUtil.equals(o1, o2);
    }

    /**
     * 初始化变更记录对象(需要子类根据实际情况重写)
     *
     * @return
     */
    protected abstract T initChangeLog();

    /**
     * 比较两个对象并获取其中不相等的字段
     *
     * @param obj1 对象1
     * @param obj2 对象2
     * @return
     */
    public List<T> compareFields(Object obj1, Object obj2) {
        try {
            List<T> changeLogList = new LinkedList<>();
            //只有两个对象都是同一类型才有可比性
            if (obj1.getClass() != obj2.getClass()) {
                return changeLogList;
            }
            List<Field> fieldList = new ArrayList<>();
            Class tempClass = obj1.getClass();
            while (tempClass != null) {//当父类为null的时候说明到达了最上层的父类(Object类).
                fieldList.addAll(Arrays.asList(tempClass.getDeclaredFields()));
                tempClass = tempClass.getSuperclass(); //得到父类,然后赋给自己
            }
            Long userId = LoginHelper.getUserId();
            Date now = new Date();
            for (Field field : fieldList) {
                //设置对象的访问权限，保证对private的属性的访问
                field.setAccessible(true);
                //只有使用@ChangeLogDesc注解标注的属性才会生成ChangeLog
                ChangeLog annotation = field.getAnnotation(ChangeLog.class);
                if (annotation == null) {
                    continue;
                }
                String propName = field.getName();
                //获取obj1、obj2的属性值
                Object o1 = field.get(obj1);
                Object o2 = field.get(obj2);
                //如果不相等构建ChangeLog放入集合中
                if (!isEquals(o1, o2)) {
                    //初始化ChangeLog
                    T changeLog = initChangeLog();
                    //属性名
                    changeLog.setFieldName(propName);
                    if (o1 instanceof Date) {
                        o1 = DateUtil.formatDate((Date) o1);
                    }
                    if (o2 instanceof Date) {
                        o2 = DateUtil.formatDate((Date) o2);
                    }
                    //变更前
                    changeLog.setOldValue(o1 != null ? String.valueOf(o1) : "");
                    //变更后
                    changeLog.setNewValue(o2 != null ? String.valueOf(o2) : "");

                    changeLog.setCreateBy(userId.intValue());
                    changeLog.setCreateTime(now);
                    changeLog.setUpdateBy(userId.intValue());
                    changeLog.setUpdateTime(now);
                    changeLogList.add(changeLog);
                }
            }
            return changeLogList;
        } catch (Exception e) {
            throw new ServiceException("比对过程中发生异常", e);
        }
    }
}
