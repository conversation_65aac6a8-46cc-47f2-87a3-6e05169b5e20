package com.zjwly.common.base;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/4/19 16:11
 * @Description
 * @Modified By:
 * @Version:1.0.0
 */
@Data
/**全局统一返回结果*/
public class Result<T> {

    /**
     * 返回码
     */
    private Integer code;

    /**
     * 返回消息
     */
    private String message;

    /**
     * 返回数据
     */
    private T data;

    /**
     * token剩余时间
     */
    private Long tokenTime;

    public Result() {
    }

    // 返回数据
    protected static <T> Result<T> build(T data) {
        Result<T> result = new Result<T>();
        if (data != null)
            result.setData(data);
        return result;
    }

    public static <T> Result<T> build(T body, ResultCodeEnum resultCodeEnum) {
        Result<T> result = build(body);
        result.setCode(resultCodeEnum.getCode());
        result.setMessage(resultCodeEnum.getMessage());

        return result;
    }

    public static <T> Result<T> ok() {
        return Result.ok(null);
    }

    /**
     * 操作成功
     *
     * @param data
     * @param <T>
     * @return
     */
    public static <T> Result<T> ok(T data) {
        Result<T> result = build(data);
        return build(data, ResultCodeEnum.SUCCESS);
    }

    public static <T> Result<T> fail() {
        return Result.fail(null);
    }

    /**
     * 操作失败
     *
     * @param data
     * @param <T>
     * @return
     */
    public static <T> Result<T> fail(T data) {
        Result<T> result = build(data);
        return build(data, ResultCodeEnum.FAIL);
    }

    public static <T> Result<T> fail(T data, Integer code) {
        ResultCodeEnum resultCodeEnum = getResultByCode(code);
        return build(data, resultCodeEnum);
    }

    private static ResultCodeEnum getResultByCode(Integer code) {
        ResultCodeEnum[] values = ResultCodeEnum.values();
        for (ResultCodeEnum resultCodeEnum : values) {
            if (resultCodeEnum.getCode().compareTo(code) == 0) {
                return resultCodeEnum;
            }
        }
        return ResultCodeEnum.FAIL;
    }

    public Result<T> message(String msg) {
        this.setMessage(msg);
        return this;
    }

    public Result<T> code(Integer code) {
        this.setCode(code);
        return this;
    }

    public boolean isOk() {
        if (this.getCode().intValue() == ResultCodeEnum.SUCCESS.getCode().intValue()) {
            return true;
        }
        return false;
    }
}
