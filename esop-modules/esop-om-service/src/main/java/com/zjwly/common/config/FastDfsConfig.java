package com.zjwly.common.config;

import com.github.tobato.fastdfs.FdfsClientConfig;
import com.github.tobato.fastdfs.service.DefaultFastFileStorageClient;
import com.github.tobato.fastdfs.service.FastFileStorageClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * FastDFS配置类
 * <AUTHOR>
 * @date 2025/03/20 19:30
 */
@Configuration
@Import(FdfsClientConfig.class)
public class FastDfsConfig {
    @Bean
    public FastFileStorageClient fastFileStorageClient() {
        return new DefaultFastFileStorageClient();
    }
}
