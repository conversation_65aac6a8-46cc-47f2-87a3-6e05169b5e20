package com.zjwly.psoms;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import org.junit.jupiter.api.Test;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;

/**
 * 测试
 *
 * <AUTHOR>
 * @date 2023/10/13 19:21
 */
public class MyTest {
    @Test
    public void dateTest() {
        // Java8  LocalDate
        LocalDate date = LocalDate.parse("2023-08-01");

        // 该月第一天
        LocalDate firstDay = date.with(TemporalAdjusters.firstDayOfMonth());
        // 该月最后一天
        LocalDate lastDay = date.with(TemporalAdjusters.lastDayOfMonth());
        // 该月的第一个周一
        LocalDate start = date.with(TemporalAdjusters.firstInMonth(DayOfWeek.MONDAY));
        System.out.println(start);

        // 该季度第一天
        DateTime beginOfQuarter = DateUtil.beginOfQuarter(DateUtil.parseDate("2024-05-22"));
        System.out.println(beginOfQuarter);
        // 该季度最后一天
        DateTime endOfQuarter = DateUtil.endOfQuarter(DateUtil.parseDate("2024-05-22"));
        System.out.println(endOfQuarter);
    }

    @Test
    public void between() {
        Date start = DateUtil.parseDate("2023-10-07");
        Date end = DateUtil.parseDate("2023-11-02");

        long between = DateUtil.weekOfYear(end) - DateUtil.weekOfYear(start);
        System.out.println(between);
    }

    @Test
    public void div() {
        int div = (int) NumberUtil.div(12, 7, 0);
        System.out.println(div);
    }

    @Test
    public void betweenMonth() {
        Date start = DateUtil.parseDate("2023-10-07");
        Date end = DateUtil.parseDate("2023-11-02");

        long between = DateUtil.betweenMonth(start, end, true);
        System.out.println(between);
    }
}
