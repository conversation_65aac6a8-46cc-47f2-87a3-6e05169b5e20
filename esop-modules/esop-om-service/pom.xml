<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zjwly</groupId>
        <artifactId>esop-modules</artifactId>
        <version>${revision}</version>
    </parent>
    <artifactId>esop-om-service</artifactId>
    <description>电站运维系统</description>

    <properties>
        <commons-exec.version>1.3</commons-exec.version>
        <hutool.version>5.8.11</hutool.version>
        <fastjson.version>2.0.23</fastjson.version>
        <easyexcel.version>3.2.1</easyexcel.version>
        <fastdfs-client.version>1.27.2</fastdfs-client.version>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.zjwly</groupId>
            <artifactId>esop-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zjwly</groupId>
            <artifactId>esop-common-satoken</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zjwly</groupId>
            <artifactId>esop-system</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zjwly</groupId>
            <artifactId>esop-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
            <version>1.3.2</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-exec</artifactId>
            <version>${commons-exec.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
            <version>${fastjson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2-extension-spring5</artifactId>
            <version>${fastjson.version}</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>${easyexcel.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.tobato</groupId>
            <artifactId>fastdfs-client</artifactId>
        </dependency>
    </dependencies>
</project>
