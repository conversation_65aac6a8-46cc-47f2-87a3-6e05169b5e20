<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>esop</artifactId>
        <groupId>com.zjwly</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <modules>
        <module>esop-common-bom</module>
        <module>esop-common-social</module>
        <module>esop-common-core</module>
        <module>esop-common-doc</module>
        <module>esop-common-excel</module>
        <module>esop-common-idempotent</module>
        <module>esop-common-job</module>
        <module>esop-common-log</module>
        <module>esop-common-mail</module>
        <module>esop-common-mybatis</module>
        <module>esop-common-oss</module>
        <module>esop-common-ratelimiter</module>
        <module>esop-common-redis</module>
        <module>esop-common-satoken</module>
        <module>esop-common-security</module>
        <module>esop-common-sms</module>
        <module>esop-common-web</module>
        <module>esop-common-translation</module>
        <module>esop-common-sensitive</module>
        <module>esop-common-json</module>
        <module>esop-common-encrypt</module>
        <module>esop-common-tenant</module>
        <module>esop-common-websocket</module>
        <module>esop-common-sse</module>
    </modules>

    <artifactId>esop-common</artifactId>
    <packaging>pom</packaging>

    <description>
        common 通用模块
    </description>

</project>
