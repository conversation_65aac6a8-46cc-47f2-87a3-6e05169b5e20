package com.zjwly.common.web.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;

/**
 * Python脚本执行工具类（线程安全）
 */
public class PythonExecutor {
    private static final Logger log = LoggerFactory.getLogger(PythonExecutor.class);
    private static final int DEFAULT_TIMEOUT = 30; // 默认超时时间（秒）

    // 环境变量键名
    private static final String ENV_SCRIPT_DIR = "PYTHON_SCRIPT_DIR";
    private static final String ENV_PYTHON_PATH = "PYTHON_INTERPRETER";

    /**
     * 执行Python脚本
     * @param scriptName 脚本文件名（需位于脚本目录中）
     * @param arguments 传递给脚本的参数列表
     * @return 脚本执行结果
     * @throws PythonExecutionException 执行异常
     */
    public ScriptResult execute(String scriptName, List<String> arguments)
        throws PythonExecutionException {

        // 1. 参数校验
        validateScriptName(scriptName);

        // 2. 构建完整命令
        final String pythonExe = resolvePythonInterpreter();
        final Path scriptPath = resolveScriptPath(scriptName);
        final List<String> command = buildCommand(pythonExe, scriptPath, arguments);

        // 3. 执行进程
        try {
            return executeProcess(command, scriptPath.getParent().toString());
        } catch (Exception e) {
            throw new PythonExecutionException("Python脚本执行失败: " + e.getMessage(), e);
        }
    }

    private List<String> buildCommand(String pythonExe, Path scriptPath, List<String> arguments) {
        List<String> cmd = new ArrayList<>(3 + arguments.size());
        cmd.add(pythonExe);
        cmd.add(scriptPath.toString());
        cmd.addAll(arguments);
        return cmd;
    }

    private String resolvePythonInterpreter() {
        // 优先从环境变量获取
        String envPython = System.getenv(ENV_PYTHON_PATH);
        if (envPython != null && !envPython.trim().isEmpty()) {
            Path pythonPath = Paths.get(envPython);
            if (Files.exists(pythonPath)) {
                return pythonPath.toString();
            }
            log.warn("环境变量{}指定的Python解释器不存在: {}", ENV_PYTHON_PATH, envPython);
        }

        // 根据操作系统选择默认值
        return System.getProperty("os.name").toLowerCase().contains("win")
            ? "python3.exe"
            : "python3";
    }

    private Path resolveScriptPath(String scriptName) throws PythonExecutionException {
        // 优先从环境变量获取脚本目录
        String scriptDir = "/home/<USER>/script/python/";
        if (System.getProperty("os.name").toLowerCase().contains("win")) {
            // Windows系统下默认使用当前工作目录下的scripts目录
            scriptDir = "d:/project/script/python/";
        }

        Path scriptPath = Paths.get(scriptDir, scriptName);
        validateScriptFile(scriptPath);
        return scriptPath;
    }

    private void validateScriptName(String scriptName) {
        if (scriptName == null || scriptName.trim().isEmpty()) {
            throw new IllegalArgumentException("脚本名称不能为空");
        }
        if (!scriptName.endsWith(".py")) {
            log.warn("执行的脚本文件没有.py扩展名: {}", scriptName);
        }
    }

    private void validateScriptFile(Path scriptPath) throws PythonExecutionException {
        if (!Files.exists(scriptPath)) {
            throw new PythonExecutionException("脚本文件不存在: " + scriptPath);
        }
        if (!Files.isReadable(scriptPath)) {
            throw new PythonExecutionException("没有读取脚本文件的权限: " + scriptPath);
        }
    }

    private ScriptResult executeProcess(List<String> command, String workingDir)
        throws IOException, InterruptedException, TimeoutException {

        ProcessBuilder pb = new ProcessBuilder(command)
            .directory(new File(workingDir))
            .redirectErrorStream(false); // 分离标准输出和错误流

        log.info("执行命令: {}", String.join(" ", command));
        System.out.println("执行命令: " + String.join(" ", command));

        Process process = null;
        try {
            process = pb.start();
            return readProcessOutput(process);
        } finally {
            if (process != null && process.isAlive()) {
                process.destroyForcibly();
            }
        }
    }

    private ScriptResult readProcessOutput(Process process)
        throws InterruptedException, TimeoutException {

        ExecutorService executor = Executors.newFixedThreadPool(2);
        try {
            // 并行读取输出流和错误流
            Future<String> stdoutFuture = executor.submit(
                () -> readStream(process.getInputStream()));

            Future<String> stderrFuture = executor.submit(
                () -> readStream(process.getErrorStream()));

            // 等待进程结束（带超时）
            boolean exited = process.waitFor(DEFAULT_TIMEOUT, TimeUnit.SECONDS);
            if (!exited) {
                throw new TimeoutException("脚本执行超时");
            }

            // 获取执行结果
            int exitCode = process.exitValue();
            String output = stdoutFuture.get(5, TimeUnit.SECONDS); // 流读取超时
            String error = stderrFuture.get(5, TimeUnit.SECONDS);

            return new ScriptResult(exitCode, output, error);
        } catch (ExecutionException e) {
            throw new RuntimeException("流读取失败", e);
        } finally {
            executor.shutdownNow();
        }
    }

    private String readStream(InputStream inputStream) throws IOException {
        try (BufferedReader reader = new BufferedReader(
            new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {

            StringBuilder sb = new StringBuilder();
            char[] buffer = new char[1024];
            int bytesRead;
            while ((bytesRead = reader.read(buffer)) != -1) {
                sb.append(buffer, 0, bytesRead);
            }
            return sb.toString();
        }
    }

    /**
     * 脚本执行结果封装
     */
    public static class ScriptResult {
        private final int exitCode;
        private final String stdout;
        private final String stderr;

        public ScriptResult(int exitCode, String stdout, String stderr) {
            this.exitCode = exitCode;
            this.stdout = stdout != null ? stdout : "";
            this.stderr = stderr != null ? stderr : "";
        }

        // Getters
        public boolean isSuccess() { return exitCode == 0; }
        public int getExitCode() { return exitCode; }
        public String getStdout() { return stdout; }
        public String getStderr() { return stderr; }
    }

    /**
     * 自定义执行异常
     */
    public static class PythonExecutionException extends Exception {
        public PythonExecutionException(String message) { super(message); }
        public PythonExecutionException(String message, Throwable cause) { super(message, cause); }
    }
}
