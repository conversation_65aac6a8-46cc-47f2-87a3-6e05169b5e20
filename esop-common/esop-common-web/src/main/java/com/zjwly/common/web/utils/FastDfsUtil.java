package com.zjwly.common.web.utils;

import cn.hutool.core.io.FileUtil;
import com.github.tobato.fastdfs.domain.fdfs.StorePath;
import com.github.tobato.fastdfs.domain.proto.storage.DownloadByteArray;
import com.github.tobato.fastdfs.service.FastFileStorageClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;

/**
 * FastDFS工具类
 *
 * <AUTHOR>
 * @date 2023/09/12 10:39
 */
@Component
public class FastDfsUtil {

    @Value("${fdfs.group}")
    private String group;

    @Autowired
    FastFileStorageClient fastFileStorageClient;


    /**
     * 上传文件
     *
     * @param file 待上传文件
     * @return com.github.tobato.fastdfs.domain.fdfs.StorePath
     * <AUTHOR>
     * @date 2023/09/12 10:52
     */
    public String uploadFile(File file) {
        long size = FileUtil.size(file);
        String suffix = FileUtil.getSuffix(file);
        BufferedInputStream inputStream = FileUtil.getInputStream(file);
        StorePath storePath = fastFileStorageClient.uploadFile(group, inputStream, size, suffix);
        return storePath.getFullPath();
    }

    public String uploadFile(MultipartFile file) throws IOException {


        InputStream inputStream = file.getInputStream();

        String suffix  =file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1);


        long size = inputStream.available();


        StorePath storePath = fastFileStorageClient.uploadFile(group, inputStream,size,suffix);

        return storePath.getFullPath();
    }

    /**
     * 下载文件字节数组
     *
     * @param filePath 存储文件的路径信息
     * @return byte[] 文件字节数组
     * <AUTHOR>
     * @date 2023/09/12 11:12
     */
    public byte[] downloadByteArray(String filePath) {
        DownloadByteArray callback = new DownloadByteArray();
        int index = filePath.indexOf("/");
        String group = filePath.substring(0, index);
        String path = filePath.substring(index + 1);
        return fastFileStorageClient.downloadFile(group, path, callback);
    }

    /**
     * 删除文件
     *
     * @param path 存储文件的路径信息
     * @return void
     * <AUTHOR>
     * @date 2023/09/12 11:05
     */
    public void deleteFile(String path) {
        fastFileStorageClient.deleteFile(path);
    }
}
