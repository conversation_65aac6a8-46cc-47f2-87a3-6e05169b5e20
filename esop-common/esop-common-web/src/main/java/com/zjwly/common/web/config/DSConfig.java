package com.zjwly.common.web.config;

import com.zjwly.common.web.interceptor.DynamicDatasourceInterceptor;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 多数据源切换配置
 *
 * <AUTHOR>
 * @date 2025/03/19 14:30
 */
//@AutoConfiguration
public class DSConfig implements WebMvcConfigurer {
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new DynamicDatasourceInterceptor()).addPathPatterns("/ds/**");
    }
}
