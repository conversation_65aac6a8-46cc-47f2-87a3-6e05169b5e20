package com.zjwly.common.web.filter;

import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.zjwly.common.core.exception.ServiceException;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;

/**
 * 多数据源切换过滤器
 *
 * <AUTHOR>
 * @date 2025/03/19 15:15
 */
@Slf4j
//@WebFilter(filterName = "dsFilter", urlPatterns = {"/ds/*"})
public class DynamicDatasourceFilter implements Filter {

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        log.info("loading filter {}", filterConfig.getFilterName());
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse,
                         FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;

        String requestURI = request.getRequestURI();
        log.debug("经过多数据源filter,当前路径是{}", requestURI);
        String headerDs = request.getHeader("ds");
//        Object sessionDs = request.getSession().getAttribute("ds");
//        String s = requestURI.replaceFirst("/ds/", "");
        if (StrUtil.isBlank(headerDs)) {
            throw new ServiceException("请求头ds参数为空");
        }
        //执行
        try {
            DynamicDataSourceContextHolder.push(headerDs);
            filterChain.doFilter(servletRequest, servletResponse);
        } finally {
            DynamicDataSourceContextHolder.poll();
        }
    }

    @Override
    public void destroy() {

    }
}
